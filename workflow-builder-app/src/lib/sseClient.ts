// No import needed for standard EventSource - it's built into modern browsers
// Removed: import { EventSourcePolyfill } from 'event-source-polyfill';
import { API_ENDPOINTS } from "./apiConfig";

export interface SSEOptions {
  onOpen?: () => void;
  onMessage?: (event: MessageEvent) => void; // For generic messages
  onCustomEvent?: (eventType: string, data: any) => void; // For named events
  onError?: (error: Event | Error) => void; // Allow Error type for internal/connection errors
  onClose?: (wasError?: boolean) => void; // Optional flag: true if closed due to error/reconnect failure
}

export interface SSEConfig {
  baseUrl?: string;
  // Note: Standard EventSource doesn't support custom headers directly.
  // Authentication must be handled via other means (cookies, query params - use with caution).
  // apiKey?: string; // Keep if needed for URL construction, etc.
  // keyType?: string; // Keep if needed for URL construction, etc.
}

export class SSEClient {
  private eventSource: EventSource | null = null;
  private correlationId: string;
  private options: SSEOptions;
  private config: SSEConfig;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5; // Adjust as needed
  private reconnectDelay: number = 2000; // 2 seconds
  private url: string;

  // --- State Flags ---
  // Flag prevents reconnect if user explicitly called close()
  private explicitlyClosed: boolean = false;
  // Flag prevents reconnect if a designated server error *event* was received (e.g., event: error)
  private receivedServerErrorEvent: boolean = false;
  // Flag prevents reconnect if a message indicated a terminal workflow status
  private receivedTerminalStatus: boolean = false;

  constructor(correlationId: string, options: SSEOptions = {}, config: SSEConfig = {}) {
    this.correlationId = correlationId;
    this.options = options;
    // Construct the URL - ensure backend handles auth via cookies or secure URL params if needed
    const baseUrl =
      config.baseUrl || process.env.NEXT_PUBLIC_SSE_URL || API_ENDPOINTS.WORKFLOW_EXECUTION.STREAM;
    this.url = `${baseUrl}/${this.correlationId}`;

    this.config = {
      ...config,
      baseUrl: baseUrl,
    };

    if (!this.correlationId) {
      console.error("SSEClient: Correlation ID is required.");
      // Consider throwing an error or handling more robustly
      if (this.options.onError) {
        // Use request time for the error if available, otherwise fallback
        const errorTime = new Date().toISOString(); // Example timestamp
        this.options.onError(new Error(`[${errorTime}] Correlation ID is required`));
      }
    }
  }

  connect(): void {
    const connectTime = new Date().toISOString(); // Example timestamp
    if (!this.correlationId) {
      console.error(`[${connectTime}] SSEClient: Cannot connect without Correlation ID.`);
      if (this.options.onError) {
        this.options.onError(new Error(`[${connectTime}] Cannot connect without Correlation ID`));
      }
      return;
    }

    if (this.isConnected()) {
      console.log(`[${connectTime}] SSEClient: Connection attempt skipped. Already connected.`);
      return;
    }

    // --- Reset flags for the new connection attempt ---
    this.reset();

    // Close any existing connection cleanly before creating a new one
    this.closeInternal(false); // Don't trigger onClose callback here

    try {
      console.log(
        `[${connectTime}] SSEClient: Connecting to ${this.url} for correlation ID: ${this.correlationId}`,
      );

      // --- Use standard EventSource ---
      // Removed polyfill-specific options like headers
      this.eventSource = new EventSource(this.url);
      // Note: If you require authentication and can't use cookies,
      // you might need to include tokens in the URL (less secure)
      // e.g., new EventSource(`${this.url}?token=YOUR_TOKEN`)
      // Ensure your server supports and secures this method.

      console.log(
        `[${connectTime}] SSEClient: EventSource instance created. Waiting for connection to open...`,
      );

      // --- Event Handlers ---

      this.eventSource.onopen = () => {
        const openTime = new Date().toISOString();
        console.log(
          `[${openTime}] SSEClient: Connection opened successfully for ${this.correlationId}.`,
        );
        this.reconnectAttempts = 0; // Reset reconnect attempts on successful open
        if (this.options.onOpen) {
          this.options.onOpen();
        }
      };

      // --- Generic Message Handler ---
      this.eventSource.onmessage = (event: MessageEvent) => {
        const messageTime = new Date().toISOString();
        // console.log(`[${messageTime}] SSEClient: Received generic message:`, event.data);
        try {
          const data = JSON.parse(event.data);
          // console.log(`[${messageTime}] SSEClient: Parsed generic message data:`, data);

          // --- Check for Terminal Workflow Status ---
          this.checkAndHandleTerminalStatus(data, "generic message");

          if (this.options.onMessage) {
            this.options.onMessage(event); // Pass the raw event
          }
        } catch (error) {
          console.error(
            `[${messageTime}] SSEClient: Error parsing generic message data:`,
            error,
            event.data,
          );
          // Optionally trigger onError for parsing failures
          // if (this.options.onError) this.options.onError(new Error(`[${messageTime}] Failed to parse message: ${error}`));
        }
      };

      // --- Connection Error Handler ---
      this.eventSource.onerror = (errorEvent: Event) => {
        const errorTime = new Date().toISOString();
        console.error(`[${errorTime}] SSEClient: Connection error occurred.`, errorEvent);

        const currentReadyState = this.eventSource?.readyState;
        const readyStateText = this.getReadyStateText(currentReadyState);
        console.log(`[${errorTime}] SSEClient: EventSource readyState on error: ${readyStateText}`);

        // Store flags before closing, as closeInternal might reset them indirectly
        const wasExplicitlyClosed = this.explicitlyClosed;
        const hadServerErrorEvent = this.receivedServerErrorEvent;
        const hadTerminalStatus = this.receivedTerminalStatus;

        // Close the faulty EventSource instance
        this.closeInternal(true); // Mark as closed due to error

        // --- Reconnection Logic ---
        if (wasExplicitlyClosed) {
          console.log(
            `[${errorTime}] SSEClient: Connection error occurred after explicit close. No reconnection attempt.`,
          );
          return;
        }

        if (hadServerErrorEvent) {
          console.log(
            `[${errorTime}] SSEClient: Connection error occurred after receiving a server error event. No reconnection attempt.`,
          );
          if (this.options.onClose) this.options.onClose(true); // Indicate closed due to error
          return;
        }

        if (hadTerminalStatus) {
          console.log(
            `[${errorTime}] SSEClient: Connection error occurred after receiving a terminal workflow status. No reconnection attempt.`,
          );
          if (this.options.onClose) this.options.onClose(true); // Indicate closed due to terminal status
          return;
        }

        // --- Attempt Reconnection ---
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          console.log(
            `[${errorTime}] SSEClient: Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay / 1000}s...`,
          );
          // Use arrow function to maintain 'this' context
          setTimeout(() => this.connect(), this.reconnectDelay);
        } else {
          console.error(
            `[${errorTime}] SSEClient: Max reconnect attempts (${this.maxReconnectAttempts}) reached for ${this.correlationId}. Giving up.`,
          );
          if (this.options.onError) {
            this.options.onError(
              new Error(
                `[${errorTime}] Failed to reconnect after ${this.maxReconnectAttempts} attempts.`,
              ),
            );
          }
          if (this.options.onClose) this.options.onClose(true); // Indicate closed due to failure
        }

        // Call user's onError *after* handling reconnection logic attempt or decision
        if (this.options.onError) {
          this.options.onError(
            new Error(`[${errorTime}] SSE connection error (readyState: ${readyStateText})`),
          );
        }
      };

      // --- Custom Event Listeners ---
      const terminalErrorEventTypes = ["error", "fatal_error"]; // Events indicating server errors that should stop reconnects
      const listenedEventTypes = [
        // Add all event types your server might send
        "connection",
        "keep-alive",
        "workflow-completed",
        "workflow-failed",
        "workflow-cancelled",
        "workflow-update",
        "error",
        "update",
        "message",
      ];

      listenedEventTypes.forEach((eventType) => {
        if (!this.eventSource) return; // Type guard

        this.eventSource.addEventListener(eventType, (event: Event) => {
          const eventTime = new Date().toISOString();
          const messageEvent = event as MessageEvent; // Assume MessageEvent
          // console.log(`[${eventTime}] SSEClient: Received event [${eventType}]:`, messageEvent.data);

          try {
            const data = messageEvent.data ? JSON.parse(messageEvent.data) : null;
            // console.log(`[${eventTime}] SSEClient: Parsed event [${eventType}] data:`, data);

            // Check if this event type itself signals a terminal server error
            if (terminalErrorEventTypes.includes(eventType)) {
              console.warn(
                `[${eventTime}] SSEClient: Received terminal server error event [${eventType}]. Flagging to prevent reconnect.`,
              );
              this.receivedServerErrorEvent = true;
              // Optionally close immediately
              // setTimeout(() => this.close(), 50);
            }

            // --- Check for Terminal Workflow Status within the event data ---
            this.checkAndHandleTerminalStatus(data, `event [${eventType}]`);

            // Call specific custom event handler
            if (this.options.onCustomEvent) {
              this.options.onCustomEvent(eventType, data);
            }
          } catch (error) {
            console.error(
              `[${eventTime}] SSEClient: Error parsing event [${eventType}] data:`,
              error,
              messageEvent.data,
            );
            // Optionally trigger onError
            // if (this.options.onError) this.options.onError(new Error(`[${eventTime}] Failed to parse ${eventType} event: ${error}`));
          }
        });
      });
    } catch (error) {
      // Catches errors during `new EventSource()` instantiation (e.g., network error, invalid URL immediately)
      const initErrorTime = new Date().toISOString();
      console.error(`[${initErrorTime}] SSEClient: Failed to create EventSource instance:`, error);
      this.eventSource = null; // Ensure it's null
      if (this.options.onError) {
        this.options.onError(
          error instanceof Error
            ? error
            : new Error(`[${initErrorTime}] Failed to initialize SSE connection`),
        );
      }
      // Decide if instantiation errors should trigger reconnect attempts
      // Usually they indicate config issues, but you could add retry logic here too:
      // if (this.reconnectAttempts < this.maxReconnectAttempts) { ... } else { onClose(true); }
    }
  }

  // --- Helper to check for terminal status ---
  private checkAndHandleTerminalStatus(data: any, context: string): void {
    const checkTime = new Date().toISOString();
    // Check if data is an object and has the workflow_status property
    if (data && typeof data === "object" && data.hasOwnProperty("workflow_status")) {
      const status = data.workflow_status;
      const terminalStatuses = ["completed", "cancelled", "failed"]; // Define terminal statuses

      // Check for approval status and dispatch a custom event
      // Only trigger approval UI when we have the specific criteria:
      // 1. workflow_status is "waiting_for_approval"
      // 2. approval_required is true
      // 3. status is "paused"
      if (
        status?.toLowerCase() === "waiting_for_approval" &&
        data.approval_required === true &&
        data.status === "paused"
      ) {
        console.log(
          `[${checkTime}] SSEClient: Received valid approval request in ${context}.`,
          data,
        );

        // Use node_id if available, otherwise fall back to transition_id
        const nodeId = data.node_id || data.transition_id;
        const nodeName = data.node_name || data.node_label || nodeId;

        if (nodeId && nodeId !== "unknown") {
          console.log(`[${checkTime}] SSEClient: Using ${data.node_id ? 'node_id' : 'transition_id'} for approval: ${nodeId}`);

          // Import dynamically to avoid circular dependencies
          import("@/lib/approvalUtils")
            .then(({ dispatchApprovalNeededEvent }) => {
              // Use the centralized function to dispatch the event
              dispatchApprovalNeededEvent(
                this.correlationId,
                nodeId,
                nodeName,
              );

              // Also set a direct flag on the window for immediate access
              // This helps with race conditions where the event might not be processed
              window._pendingApproval = {
                correlationId: this.correlationId,
                nodeId: nodeId,
                nodeName: nodeName,
                timestamp: Date.now(),
              };

              // Force a UI update by dispatching a direct event
              setTimeout(() => {
                console.log(
                  `[${checkTime}] SSEClient: Dispatching direct approval-ui-update event`,
                );
                window.dispatchEvent(new CustomEvent("approval-ui-update"));
              }, 500);
            })
            .catch((err) => {
              console.error(`[${checkTime}] SSEClient: Error importing approvalUtils:`, err);

              // Fallback to direct event dispatch if import fails
              const timestamp = Date.now();
              const event = new CustomEvent("workflow-approval-needed", {
                detail: {
                  correlationId: this.correlationId,
                  nodeId: nodeId,
                  nodeName: nodeName,
                  timestamp: timestamp,
                  // Add a force flag to ensure this event is processed
                  force: true,
                },
              });
              window.dispatchEvent(event);

              // Also set the window flag
              window._pendingApproval = {
                correlationId: this.correlationId,
                nodeId: nodeId,
                nodeName: nodeName,
                timestamp: timestamp,
              };
            });
        } else {
          console.log(`[${checkTime}] SSEClient: Skipping approval event due to missing node_id and transition_id`);
        }

        // Do NOT close the connection - keep streaming
        return;
      } else if (status?.toLowerCase() === "waiting_for_approval") {
        // Log that we received a waiting_for_approval status but it didn't meet our criteria
        console.log(
          `[${checkTime}] SSEClient: Received waiting_for_approval status but it didn't meet criteria for approval UI:`,
          data,
        );
      }

      if (terminalStatuses.includes(status?.toLowerCase())) {
        console.log(
          `[${checkTime}] SSEClient: Received terminal workflow status "${status}" in ${context}. Flagging to prevent reconnect.`,
        );
        this.receivedTerminalStatus = true;

        // Log the status for debugging
        console.log(
          `[${checkTime}] SSEClient: Workflow status: ${status}, Node ID: ${data.node_id || "unknown"}, Result: ${data.result || "none"}`,
        );

        // Close the connection proactively after receiving a terminal status
        console.log(
          `[${checkTime}] SSEClient: Closing connection shortly due to terminal status "${status}".`,
        );

        // Dispatch a custom event to notify listeners that a terminal status was received
        // This can be used to update UI elements
        const event = new CustomEvent("workflow-terminal-status", {
          detail: { status, nodeId: data.node_id, result: data.result },
        });
        window.dispatchEvent(event);

        // Force close the connection immediately for complete status
        if (status?.toLowerCase() === "complete") {
          console.log(
            `[${checkTime}] SSEClient: Immediately closing connection for "complete" status.`,
          );
          this.close();
        } else {
          // For other terminal statuses, close after a short delay to allow any final messages to be received
          setTimeout(() => this.close(), 500);
        }
      }
    }
  }

  // --- Public close method ---
  close(): void {
    const closeTime = new Date().toISOString();
    // Only log if we actually have something to close
    if (this.eventSource) {
      console.log(
        `[${closeTime}] SSEClient: Explicitly closing connection for ${this.correlationId}...`,
      );
      this.explicitlyClosed = true; // Set flag to prevent reconnects by onerror
      // Stop any scheduled reconnection attempts
      this.reconnectAttempts = this.maxReconnectAttempts;
      this.closeInternal(false); // Close the connection, not marked as an error closure
    } else {
      console.log(`[${closeTime}] SSEClient: Close called, but no active connection.`);
    }
  }

  // --- Internal close method ---
  private closeInternal(isDueToError: boolean): void {
    if (this.eventSource) {
      // Close the EventSource
      this.eventSource.close();
      this.eventSource = null;

      // Call onClose only if:
      // 1. It was an explicit close (isDueToError=false AND explicitlyClosed=true)
      // 2. It was due to an error that won't be retried (handled in onerror logic)
      // Avoid calling onClose here if an error occurred and a retry might happen.
      // The onerror handler will call onClose if max retries are reached or retry is skipped.
      if (!isDueToError && this.explicitlyClosed) {
        if (this.options.onClose) {
          // Pass 'false' because it wasn't an *unexpected* error closure
          this.options.onClose(false);
        }
      }
    }
  }

  isConnected(): boolean {
    // Also check explicitlyClosed flag, connection might be closing but state not yet CLOSED
    return (
      this.eventSource !== null &&
      this.eventSource.readyState === EventSource.OPEN &&
      !this.explicitlyClosed
    );
  }

  getReadyState(): number | null {
    return this.eventSource?.readyState ?? null;
  }

  private getReadyStateText(readyState: number | null | undefined): string {
    switch (readyState) {
      case EventSource.CONNECTING:
        return "CONNECTING (0)";
      case EventSource.OPEN:
        return "OPEN (1)";
      case EventSource.CLOSED:
        return "CLOSED (2)";
      case null:
        return "NULL (Not Initialized)";
      case undefined:
        return "UNDEFINED (Error State)"; // Should ideally not happen
      default:
        return `UNKNOWN (${readyState})`;
    }
  }

  // Reset all internal state flags to allow reconnection
  reset(): void {
    console.log(`SSEClient: Resetting internal state flags for ${this.correlationId}`);
    this.explicitlyClosed = false;
    this.receivedServerErrorEvent = false;
    this.receivedTerminalStatus = false;
    this.reconnectAttempts = 0;
  }
}
