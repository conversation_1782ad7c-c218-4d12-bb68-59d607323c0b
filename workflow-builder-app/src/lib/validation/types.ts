import { Node, <PERSON> } from "reactflow";
import { WorkflowNodeData } from "@/types";

/**
 * Validation error codes for all validation rules
 */
export enum ValidationErrorCode {
  // Workflow structure errors
  WORKFLOW_INVALID_JSON = "WF001",
  WORKFLOW_MISSING_NODES = "WF002",
  WORKFLOW_MISSING_EDGES = "WF003",
  WORKFLOW_MISSING_START_NODE = "WF004",
  WORKFLOW_DISCONNECTED_NODES = "WF005",
  WORKFLOW_CYCLE_DETECTED = "WF006",
  WORKFLOW_INVALID_NAME = "WF007",
  WORKFLOW_EMPTY = "WF008",
  WORKFLOW_USING_FALLBACK_START_NODE = "WF009",

  // Node validation errors
  NODE_MISSING_ID = "ND001",
  NODE_MISSING_TYPE = "ND002",
  NODE_MISSING_POSITION = "ND003",
  NODE_MISSING_DATA = "ND004",
  NODE_MISSING_DATA_TYPE = "ND005",
  NODE_MISSING_DATA_LABEL = "ND006",
  NODE_MISSING_DATA_DEFINITION = "ND007",
  NODE_DUPLICATE_ID = "ND008",
  NODE_INVALID_POSITION = "ND009",

  // Edge validation errors
  EDGE_MISSING_ID = "ED001",
  EDGE_MISSING_SOURCE = "ED002",
  EDGE_MISSING_TARGET = "ED003",
  EDGE_SOURCE_NOT_FOUND = "ED004",
  EDGE_TARGET_NOT_FOUND = "ED005",
  EDGE_DUPLICATE_ID = "ED006",
  EDGE_SELF_REFERENCE = "ED007",

  // Field validation errors
  FIELD_REQUIRED = "FD001",
  FIELD_STRING_LENGTH = "FD002",
  FIELD_NUMBER_RANGE = "FD003",
  FIELD_PATTERN_MISMATCH = "FD004",
  FIELD_MISSING_REQUIRED_KEYS = "FD005",
  FIELD_CONNECTED_INPUT = "FD006",
  NODE_INVALID_CONFIG = "NODE_INVALID_CONFIG",
}

/**
 * Validation error severity levels
 */
export type ValidationSeverity = "error" | "warning" | "info";

/**
 * Validation error interface
 */
export interface ValidationError {
  code: ValidationErrorCode;
  message: string;
  severity: ValidationSeverity;
  nodeId?: string;
  nodeLabel?: string; // Human-readable node label for better UX
  fieldId?: string;
}

/**
 * Missing field interface
 */
export interface MissingField {
  nodeId: string;
  nodeName: string;
  name: string;
  displayName: string;
  info?: string;
  inputType: string;
  connected_to_start?: boolean;
  directly_connected_to_start?: boolean;
  // Additional properties for all fields collection
  required?: boolean;
  isEmpty?: boolean;
  currentValue?: any;
  options?: Array<string | { value: string; label: string }> | null;
  schema?: {
    type?: string;
    properties?: Record<string, any>;
    required?: string[];
  };
  // Handle connection properties
  is_handle?: boolean;
  is_connected?: boolean;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  infos: ValidationError[];
  missingFields?: MissingField[];
  startNodeId?: string;
  connectedNodes?: Set<string>;
}

/**
 * Workflow validation options
 */
export interface WorkflowValidationOptions {
  validateConnectivity?: boolean;
  collectMissingFields?: boolean;
  validateFieldTypes?: boolean;
  validateCycles?: boolean;
}

/**
 * Validation state for Zustand store
 */
export interface ValidationState {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  infos: ValidationError[];
  missingFields: MissingField[];
  startNodeId?: string;
  connectedNodes?: Set<string>;
  isValidating: boolean;
  hasValidated: boolean;

  // Actions
  validateWorkflow: (
    nodes: Node<WorkflowNodeData>[],
    edges: Edge[],
    options?: WorkflowValidationOptions
  ) => ValidationResult;
  clearValidation: () => void;
}
