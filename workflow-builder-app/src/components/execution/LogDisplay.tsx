import React, { useRef, useEffect, useState } from "react";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { AlertCircle, CheckCircle, Info, XCircle, Loader2, ShieldCheck, ChevronDown, ChevronUp, Eye, Code, Clock } from "lucide-react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useExecutionStore } from "@/store/executionStore";
import { dispatchApprovalNeededEvent } from "@/lib/approvalUtils";

interface LogDisplayProps {
  logs: (string | object)[];
  showStreamingStatus?: boolean;
}

export function LogDisplay({ logs = [], showStreamingStatus = true }: LogDisplayProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const { isStreaming } = useExecutionStore();
  const [activeLogTab, setActiveLogTab] = useState<"prettify" | "raw">("prettify");

  // Helper functions for tab content
  const getPrettifyLogs = (logs: (string | object)[]) => {
    // Filter logs to only show those with node_label and have raw results
    return logs.filter((log) => {
      if (typeof log === 'object' && log !== null) {
        const logData = log as any;
        return logData.node_label !== undefined && (logData.raw_result || logData.raw_results);
      }
      return false;
    }).map((log) => {
      if (typeof log === 'object' && log !== null) {
        const logData = log as any;
        // Keep node_label, status, and raw result fields for prettify view
        const prettifiedLog: any = {
          node_label: logData.node_label,
          // Preserve status for proper node status display
          status: logData.status,
          // Preserve correlation ID for reference
          correlation_id: logData.correlation_id
        };

        // Include raw result fields
        if (logData.raw_result) {
          prettifiedLog.raw_result = logData.raw_result;
        }
        if (logData.raw_results) {
          prettifiedLog.raw_results = logData.raw_results;
        }

        return prettifiedLog;
      }
      return log;
    });
  };

  const getRawLogs = (logs: (string | object)[]) => {
    // Return all logs without any filtering
    return logs;
  };

  // Group prettified logs by node_label
  const groupLogsByNodeLabel = (logs: (string | object)[]) => {
    const grouped: Record<string, (string | object)[]> = {};

    logs.forEach((log) => {
      if (typeof log === 'object' && log !== null) {
        const logData = log as any;
        const nodeLabel = logData.node_label || 'Unknown Node';
        if (!grouped[nodeLabel]) {
          grouped[nodeLabel] = [];
        }
        grouped[nodeLabel].push(log);
      }
    });

    return grouped;
  };

  // Get status icon and loader for a node based on its logs
  const getNodeStatus = (logs: (string | object)[]) => {
    // Get the latest log for this node
    const latestLog = logs[logs.length - 1];
    if (typeof latestLog === 'object' && latestLog !== null) {
      const logData = latestLog as any;
      const status = logData.status?.toLowerCase();

      switch (status) {
        case 'initialized':
          return {
            icon: <Clock className="h-4 w-4 text-blue-500" />,
            status: 'Initialized',
            isLoading: false,
            className: 'text-blue-800 bg-blue-50 border-blue-100'
          };
        case 'started':
          return {
            icon: <Loader2 className="h-4 w-4 animate-spin text-blue-500" />,
            status: 'Started',
            isLoading: true,
            className: 'text-blue-800 bg-blue-50 border-blue-100'
          };
        case 'connecting':
          return {
            icon: <Loader2 className="h-4 w-4 animate-spin text-yellow-500" />,
            status: 'Connecting',
            isLoading: true,
            className: 'text-yellow-800 bg-yellow-50 border-yellow-100'
          };
        case 'connected':
          return {
            icon: <CheckCircle className="h-4 w-4 text-green-500" />,
            status: 'Connected',
            isLoading: false,
            className: 'text-green-800 bg-green-50 border-green-100'
          };
        case 'completed':
        case 'success':
          return {
            icon: <CheckCircle className="h-4 w-4 text-green-500" />,
            status: 'Completed',
            isLoading: false,
            className: 'text-green-800 bg-green-50 border-green-100'
          };
        case 'failed':
        case 'error':
          return {
            icon: <XCircle className="h-4 w-4 text-red-500" />,
            status: 'Failed',
            isLoading: false,
            className: 'text-red-800 bg-red-50 border-red-100'
          };
        default:
          return {
            icon: <Info className="h-4 w-4 text-gray-500" />,
            status: status || 'Unknown',
            isLoading: false,
            className: 'text-gray-800 bg-gray-50 border-gray-100'
          };
      }
    }

    return {
      icon: <Info className="h-4 w-4 text-gray-500" />,
      status: 'Unknown',
      isLoading: false,
      className: 'text-gray-800 bg-gray-50 border-gray-100'
    };
  };

  // Ensure logs is always an array of strings or objects, parsing JSON strings
  const processedLogs = (Array.isArray(logs) ? logs : []).map((log) => {
    if (log === null || log === undefined) {
      return "";
    }
    if (typeof log === "object") {
      return log; // Keep objects as objects
    }
    if (typeof log === "string") {
      try {
        const parsed = JSON.parse(log);
        if (typeof parsed === 'object' && parsed !== null) {
          return parsed; // Return parsed object if it's valid JSON
        }
      } catch (e) {
        // Not a valid JSON string, return original string
      }
    }
    return String(log); // Coerce any other types to string
  });

  // Determine workflow status from logs
  const getWorkflowStatus = (): {
    status: "in_progress" | "completed" | "failed" | "cancelled" | "waiting_for_approval";
    message: string;
    nodeId?: string;
    nodeName?: string;
  } => {
    // Default status
    let result = {
      status: "in_progress" as const,
      message: "Execution in progress...",
    };

    // Check logs for workflow status, from newest to oldest
    for (let i = processedLogs.length - 1; i >= 0; i--) {
      const log = processedLogs[i];
      let logData: any;

      // If log is already an object, use it directly
      if (typeof log === 'object' && log !== null) {
        logData = log;
      } else {
        // Try to parse the log as JSON if it's a string
        try {
          logData = JSON.parse(String(log));
        } catch (e) {
          // Not a JSON string, might be one of our formatted messages
          // Fallback to string matching for non-JSON logs
          const lowerCaseLog = String(log).toLowerCase();
          if (lowerCaseLog.includes("workflow failed")) {
            return { status: "failed", message: "Execution failed" };
          } else if (lowerCaseLog.includes("workflow completed")) {
            return { status: "completed", message: "Execution completed successfully" };
          } else if (lowerCaseLog.includes("workflow cancelled")) {
            return { status: "cancelled", message: "Execution was cancelled" };
          }
          continue; // Move to the next log if it's not JSON
        }
      }

      // If it's a JSON log, check the workflow_status
      if (logData && logData.workflow_status) {
        const status = logData.workflow_status.toLowerCase();
        switch (status) {
          case "failed":
            return { status: "failed", message: "Execution failed" };
          case "completed":
            return { status: "completed", message: "Execution completed successfully" };
          case "cancelled":
            return { status: "cancelled", message: "Execution was cancelled" };
          case "waiting_for_approval":
            // For approval, we need more specific checks
            if (logData.approval_required === true && logData.status === "paused") {
              const nodeId = logData.node_id || logData.transition_id;
              const nodeName = logData.node_name || logData.node_label || nodeId || "Unknown Node";
              return {
                status: "waiting_for_approval",
                message: "Waiting for approval",
                nodeId: nodeId || undefined,
                nodeName: nodeName,
              };
            }
            break;
          default:
            // For other statuses like 'running', we continue checking previous logs
            break;
        }
      }
    }

    return result;
  };

  // Get current workflow status
  const workflowStatus = getWorkflowStatus();
  const { correlationId } = useExecutionStore();

  // Auto-scroll to bottom when logs update
  // useEffect(() => {
  //   if (scrollRef.current) {
  //     scrollRef.current.scrollIntoView({ behavior: "smooth" });
  //   }
  // }, [processedLogs]);

  // Dispatch approval needed event when status changes to waiting_for_approval
  useEffect(() => {
    if (workflowStatus.status === "waiting_for_approval" && correlationId && workflowStatus.nodeId) {
      console.log("LogDisplay: Detected valid waiting_for_approval status, dispatching event");

      // Only dispatch if we have a valid node ID (not undefined or "unknown")
      if (workflowStatus.nodeId && workflowStatus.nodeId !== "unknown") {
        // Use the centralized function to dispatch the event
        dispatchApprovalNeededEvent(
          correlationId,
          workflowStatus.nodeId,
          workflowStatus.nodeName || workflowStatus.nodeId
        );

        // Also set the window flag directly for immediate access
        window._pendingApproval = {
          correlationId,
          nodeId: workflowStatus.nodeId,
          nodeName: workflowStatus.nodeName || workflowStatus.nodeId,
          timestamp: Date.now()
        };

        // Force a UI update
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('approval-ui-update'));
        }, 200);

        // Log the approval request for debugging
        console.log("Dispatched approval event for:", {
          correlationId,
          nodeId: workflowStatus.nodeId,
          nodeName: workflowStatus.nodeName || workflowStatus.nodeId
        });
      } else {
        console.log("Skipping approval event dispatch due to missing node ID");
      }
    }
  }, [workflowStatus.status, correlationId, workflowStatus.nodeId, workflowStatus.nodeName]);

  // Parse log line to determine type and format
  const parseLogLine = (logEntry: string | object) => {
    // Default values
    let type = "info";
    let icon = <Info className="h-4 w-4 flex-shrink-0 text-blue-500" />;
    let className = "text-blue-800 bg-blue-50 border-blue-100";

    // For structured JSON logs, check the status field first
    if (typeof logEntry === 'object' && logEntry !== null) {
      const logData = logEntry as any;
      if (logData.status) {
        const status = logData.status.toLowerCase();
        switch (status) {
          case 'completed':
          case 'success':
            type = "success";
            icon = <CheckCircle className="h-4 w-4 flex-shrink-0 text-green-500" />;
            className = "text-green-800 bg-green-50 border-green-100";
            return { type, icon, className };
          case 'failed':
          case 'error':
            type = "error";
            icon = <XCircle className="h-4 w-4 flex-shrink-0 text-red-500" />;
            className = "text-red-800 bg-red-50 border-red-100";
            return { type, icon, className };
          case 'connecting':
          case 'connected':
          case 'started':
          case 'time_logged':
            type = "info";
            icon = <Info className="h-4 w-4 flex-shrink-0 text-blue-500" />;
            className = "text-blue-800 bg-blue-50 border-blue-100";
            return { type, icon, className };
        }
      }
    }

    // Fall back to text-based analysis for unstructured logs
    const logString = typeof logEntry === 'string' ? logEntry : JSON.stringify(logEntry);

    // Check for error messages using word boundaries for more precise matching
    if (
      /\b(error|exception|failed)\b/i.test(logString) ||
      logString.toLowerCase().includes("❌")
    ) {
      type = "error";
      icon = <XCircle className="h-4 w-4 flex-shrink-0 text-red-500" />;
      className = "text-red-800 bg-red-50 border-red-100";
    }
    // Check for success messages
    else if (
      /\b(success|completed|finished)\b/i.test(logString) ||
      logString.toLowerCase().includes("✅")
    ) {
      type = "success";
      icon = <CheckCircle className="h-4 w-4 flex-shrink-0 text-green-500" />;
      className = "text-green-800 bg-green-50 border-green-100";
    }
    // Check for warning messages
    else if (/\b(warning|warn)\b/i.test(logString)) {
      type = "warning";
      icon = <AlertCircle className="h-4 w-4 flex-shrink-0 text-yellow-500" />;
      className = "text-yellow-800 bg-yellow-50 border-yellow-100";
    }

    return { type, icon, className };
  };

  // LogEntry component to handle individual log line rendering (simplified for prettify view)
  const LogEntry = ({ log, index, isPrettifyView = false }: { log: string | object; index: number; isPrettifyView?: boolean }) => {
    // For prettify view with raw results, start expanded to show raw results
    const [isExpanded, setIsExpanded] = useState(isPrettifyView);
    const { icon, className } = parseLogLine(log);

    let logData: any = null;
    let isCollapsible = false;

    if (typeof log === 'object' && log !== null) {
      logData = log;
    } else {
      try {
        logData = JSON.parse(String(log));
      } catch (e) {
        // Not a JSON log
        logData = null;
      }
    }

    // For prettify view, we don't need collapsible behavior - just show raw results directly
    if (isPrettifyView && logData && typeof logData === 'object') {
      // In prettify view, we're showing raw results so no need for collapsible behavior
      isCollapsible = false;
    }
    // For raw view, determine if log should be collapsible
    else if (!isPrettifyView && logData && typeof logData === 'object') {
      const hasWorkflowId = typeof logData.workflowId === 'string' || typeof logData.workflow_id === 'string';
      const hasTransactionId = typeof logData.transactionId === 'string' || typeof logData.transaction_id === 'string' || typeof logData.transition_id === 'string';
      const hasMessage = typeof logData.message === 'string';

      // Count how many of the key fields we have
      const fieldCount = [hasWorkflowId, hasTransactionId, hasMessage].filter(Boolean).length;

      // Only make collapsible if we have at least 2 of the key fields
      if (fieldCount >= 2) {
        isCollapsible = true;
      }
    }

    const toggleExpand = () => {
      setIsExpanded(!isExpanded);
    };

    return (
      <div
        key={index}
        className={`flex flex-col rounded border p-2 ${className}`}
      >
        {/* Special rendering for prettify view with raw results */}
        {isPrettifyView ? (
          <div className="flex flex-col">
            <div className="flex items-start gap-2">
              {icon}
              <div className="flex-1">
                <span className="font-medium">Raw Result:</span>
              </div>
            </div>
            <div className="mt-2 pl-6 text-gray-700 bg-gray-50 p-2 rounded">
              <pre className="whitespace-pre-wrap text-xs overflow-auto max-h-[300px]">
                {(() => {
                  // Display raw_result or raw_results
                  if (logData && typeof logData === 'object') {
                    if (logData.raw_result) {
                      return JSON.stringify(logData.raw_result, null, 2);
                    } else if (logData.raw_results) {
                      return JSON.stringify(logData.raw_results, null, 2);
                    }
                  }
                  return JSON.stringify(logData, null, 2);
                })()}
              </pre>
              {logData && logData.correlation_id && (
                <div className="mt-2 text-xs text-gray-500">
                  Correlation ID: {logData.correlation_id}
                </div>
              )}
            </div>
          </div>
        ) : (
          // Original rendering for raw view
          <>
            <div className="flex items-start gap-2 relative">
              {icon}
              {isCollapsible ? (
                <div className="flex-1">
                  <span className="break-all whitespace-pre-wrap pr-8">
                    {(() => {
                      const displayWorkflowId = logData.workflowId || logData.workflow_id;
                      const displayTransactionId = logData.transactionId || logData.transaction_id || logData.transition_id;
                      const displayMessage = logData.message;

                      const elements = [];

                      if (displayWorkflowId) {
                        elements.push(
                          <span key="workflowId">
                            <strong>Workflow ID:</strong> {displayWorkflowId}
                          </span>
                        );
                      }

                      if (displayTransactionId) {
                        elements.push(
                          <span key="transactionId">
                            <strong>Transaction ID:</strong> {displayTransactionId}
                          </span>
                        );
                      }

                      if (displayMessage) {
                        elements.push(
                          <span key="message">
                            <strong>Message:</strong> {displayMessage}
                          </span>
                        );
                      }

                      return elements.map((element, idx) => (
                        <React.Fragment key={element.key}>
                          {idx > 0 && <br />}
                          {element}
                        </React.Fragment>
                      ));
                    })()}
                  </span>
                  <button
                    onClick={toggleExpand}
                    className="p-1.5 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 inline-flex items-center justify-center absolute top-0 right-0"
                    aria-expanded={isExpanded}
                    aria-controls={`log-details-${index}`}
                  >
                    {isExpanded ? (
                      <ChevronUp className="h-4 w-4 text-gray-600" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-600" />
                    )}
                  </button>
                </div>
              ) : (
                <span className="break-all whitespace-pre-wrap">
                  {typeof log === 'object' && log !== null ? JSON.stringify(log, null, 2) : String(log)}
                </span>
              )}
            </div>
            {isCollapsible && isExpanded && (
              <div id={`log-details-${index}`} className="mt-2 pl-6 text-gray-700 bg-gray-50 p-2 rounded">
                <pre className="whitespace-pre-wrap text-xs">{JSON.stringify(logData, null, 2)}</pre>
              </div>
            )}
          </>
        )}
      </div>
    );
  };

  // Filter out duplicate "Workflow is waiting for approval" messages
  const filteredLogs = processedLogs.reduce((acc: (string | object)[], log: string | object, index: number) => {
    // Skip duplicate "Workflow is waiting for approval" messages
    if (
      typeof log === 'string' && log.startsWith("⏸️ Workflow is waiting for approval") &&
      index > 0 &&
      typeof processedLogs[index - 1] === 'string' && processedLogs[index - 1].startsWith("⏸️ Workflow is waiting for approval")
    ) {
      return acc;
    }

    // Add the log to the filtered list
    acc.push(log);
    return acc;
  }, []);

  // Process logs for different views
  const prettifyLogs = getPrettifyLogs(filteredLogs);
  const rawLogs = getRawLogs(filteredLogs);
  const groupedLogs = groupLogsByNodeLabel(prettifyLogs);

  // Component for rendering node label dropdown with status
  const NodeLabelDropdown = ({ nodeLabel, logs }: { nodeLabel: string; logs: (string | object)[] }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const nodeStatus = getNodeStatus(logs);

    return (
      <div className="mb-4">
        <div
          className={`flex items-center justify-between p-3 rounded border cursor-pointer ${nodeStatus.className}`}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center gap-2">
            {nodeStatus.icon}
            <span className="font-medium">{nodeLabel}</span>
            <span className="text-sm opacity-75">({nodeStatus.status})</span>
          </div>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>

        {isExpanded && (
          <div className="mt-2 space-y-1">
            {logs.map((log: string | object, index: number) => (
              <LogEntry key={`${nodeLabel}-${index}`} log={log} index={index} isPrettifyView={true} />
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderPrettifyContent = () => {
    const nodeLabels = Object.keys(groupedLogs);

    if (nodeLabels.length === 0) {
      return (
        <div className="text-muted-foreground py-8 text-center">
          <Info className="mx-auto mb-2 h-8 w-8 opacity-50" />
          <p>No logs with node labels available. Run the workflow to see execution logs.</p>
        </div>
      );
    }

    return (
      <>
        {nodeLabels.map((nodeLabel) => (
          <NodeLabelDropdown
            key={nodeLabel}
            nodeLabel={nodeLabel}
            logs={groupedLogs[nodeLabel]}
          />
        ))}

        {/* Streaming status indicator */}
        {showStreamingStatus && isStreaming && (
          <div className="flex items-center gap-2 rounded border border-blue-100 bg-blue-50 p-2 text-blue-800">
            <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            <span>Streaming logs in real-time...</span>
          </div>
        )}
      </>
    );
  };

  const renderRawContent = () => {
    return (
      <>
        {rawLogs.map((log: string | object, index: number) => (
          <LogEntry key={index} log={log} index={index} isPrettifyView={false} />
        ))}

        {/* Streaming status indicator */}
        {showStreamingStatus && isStreaming && (
          <div className="flex items-center gap-2 rounded border border-blue-100 bg-blue-50 p-2 text-blue-800">
            <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            <span>Streaming logs in real-time...</span>
          </div>
        )}

        {/* Execution status indicator */}
        {showStreamingStatus &&
          !isStreaming &&
          processedLogs.length > 0 &&
          workflowStatus.status !== "in_progress" && (
            <div
              className={`flex items-center gap-2 rounded border p-2 ${
                workflowStatus.status === "failed"
                  ? "border-red-100 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300"
                  : workflowStatus.status === "completed"
                    ? "border-green-100 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300"
                    : workflowStatus.status === "cancelled"
                      ? "border-yellow-100 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
                      : workflowStatus.status === "waiting_for_approval"
                        ? "border-blue-100 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300"
                        : "border-green-100 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300"
              }`}
            >
              {workflowStatus.status === "failed" ? (
                <XCircle className="h-4 w-4 text-red-500" />
              ) : workflowStatus.status === "completed" ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : workflowStatus.status === "cancelled" ? (
                <AlertCircle className="h-4 w-4 text-yellow-500" />
              ) : workflowStatus.status === "waiting_for_approval" ? (
                <ShieldCheck className="h-4 w-4 text-blue-500" />
              ) : (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
              <span>{workflowStatus.message}</span>
            </div>
          )}
      </>
    );
  };

  return (
    <div className="h-[400px] flex flex-col">
      {/* Tab header - Sticky */}
      <div className="flex-shrink-0 bg-card border-b border-border sticky top-0 z-40">
        <Tabs value={activeLogTab} onValueChange={(value) => setActiveLogTab(value as "prettify" | "raw")}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="prettify" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Prettify
            </TabsTrigger>
            <TabsTrigger value="raw" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Raw
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content area - Scrollable */}
      <div className="flex-1 overflow-hidden">
        <CustomScrollArea className="h-full">
          <div className="space-y-1 p-4 font-mono text-xs">
            {activeLogTab === "prettify" ? renderPrettifyContent() : renderRawContent()}
            <div ref={scrollRef} />
          </div>
        </CustomScrollArea>
      </div>
    </div>
  );
}
