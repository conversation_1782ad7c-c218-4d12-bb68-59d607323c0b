import React from "react";
import { useInspector } from "./InspectorContext";
import { DrawerFooter } from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Trash2, Save, CheckCircle } from "lucide-react";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

/**
 * Component for the footer section of the inspector panel
 */
export function InspectorFooter() {
  const { selectedNode, onClose, onDeleteNode, validateAllNodeInputs, applyAllChanges } = useInspector();

  // If no node is selected, don't render anything
  if (!selectedNode) return null;

  // Handle apply button click
  const handleApply = () => {
    // First apply all changes
    applyAllChanges();
    // Then close the panel
    onClose();
  };

  return (
    <DrawerFooter className="flex flex-shrink-0 justify-between border-t p-4">
      <div className="flex gap-2">
        {selectedNode.data.originalType !== "StartNode" && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onDeleteNode(selectedNode.id)}
            className="gap-1"
          >
            <Trash2 className="h-4 w-4" /> Delete
          </Button>
        )}
        {/* <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                onClick={validateAllNodeInputs}
                className="gap-1 border-2 border-yellow-500/30 bg-yellow-500/10 text-yellow-700 hover:bg-yellow-500/20 dark:text-yellow-400"
              >
                <CheckCircle className="h-4 w-4" /> Validate Node
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Manually validate this node's configuration.<br />Fields are not validated during editing.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider> */}
      </div>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="default" size="sm" onClick={handleApply} className="gap-1">
              <Save className="h-4 w-4" /> Apply
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Apply all changes to the node configuration.<br />Changes are also applied automatically as you type.</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </DrawerFooter>
  );
}
