import React, { useCallback, useEffect, useRef } from "react";
import { Play, Eye } from "lucide-react";
import { Node, Edge, useReactFlow } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { executeWorkflowWithValues } from "@/lib/workflow-api";
import { useWorkflowValidation } from "@/hooks/useWorkflowValidation";
import { ExecutionDialog } from "@/components/execution/ExecutionDialog";
import { useExecutionStore } from "@/store/executionStore";
import { SSEClient } from "@/lib/sseClient";
import { collectMissingRequiredFields, collectAllFields } from "@/lib/validation/fieldValidation";
import { MissingField } from "@/lib/validation/types";
import { clearAllApprovalEvents } from "@/lib/approvalUtils";
import { toast } from "sonner";

interface RunButtonProps {
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];
  disabled?: boolean;
  onRun?: () => void;
  workflowId?: string;
}

export const RunButton = React.memo(function RunButton({
  nodes,
  edges,
  disabled = false,
  onRun,
  workflowId,
}: RunButtonProps) {
  // Get state from Zustand store
  const executionStore = useExecutionStore();
  const {
    isDialogOpen,
    setDialogOpen,
    setMissingFields,
    addLog,
    clearLogs,
    setIsExecuting,
    setActiveTab,
    processFieldValues,
    setCorrelationId,
    isStreaming,
    setIsStreaming,
    hasActiveExecution,
    setHasActiveExecution,
    stopExecution,
    viewExecution,
    resetState,
  } = executionStore;

  // Get React Flow and validation functions
  const { getNodes, getEdges } = useReactFlow<WorkflowNodeData>();
  const { validateCurrentWorkflowBeforeExecution } = useWorkflowValidation();

  // Reference to the SSE client
  const sseClientRef = useRef<SSEClient | null>(null);
  // Setup SSE connection when correlation ID changes
  useEffect(() => {
    // Close any existing SSE connection when component unmounts or correlation ID changes
    return () => {
      if (sseClientRef.current) {
        sseClientRef.current.close();
        sseClientRef.current = null;
      }
    };
  }, []);

  // Listen for workflow terminal status events
  useEffect(() => {
    const handleTerminalStatus = (event: CustomEvent) => {
      console.log("RunButton: Received workflow-terminal-status event:", event.detail);
      // This will allow the Run button to be enabled again
      // We don't set isExecuting to false completely to keep showing the status

      // Make sure streaming state is set to false to enable the Run button
      setIsStreaming(false);

      // If the status is completed, ensure the SSE client is closed
      if (event.detail?.status?.toLowerCase() === "completed" && sseClientRef.current) {
        console.log("RunButton: Forcing SSE client close for completed status");
        sseClientRef.current.close();
        sseClientRef.current = null;
      }
    };

    // Add event listener
    window.addEventListener("workflow-terminal-status", handleTerminalStatus as EventListener);

    // Clean up
    return () => {
      window.removeEventListener("workflow-terminal-status", handleTerminalStatus as EventListener);
    };
  }, [setIsStreaming]);

  // Listen for workflow approval needed events
  useEffect(() => {
    const handleApprovalNeeded = (event: CustomEvent) => {
      console.log("RunButton: Received workflow-approval-needed event:", event.detail);

      // Keep the streaming state true since we're still connected
      // but update the UI to show we're waiting for approval
      addLog(`⏸️ Workflow is waiting for approval. Node: ${event.detail.nodeName || "Unknown"}`);

      // Make sure the execution dialog is open to show the approval UI
      if (!isDialogOpen) {
        setDialogOpen(true);
        setActiveTab("logs");
      }
    };

    // Add event listener
    window.addEventListener("workflow-approval-needed", handleApprovalNeeded as EventListener);

    // Clean up
    return () => {
      window.removeEventListener("workflow-approval-needed", handleApprovalNeeded as EventListener);
    };
  }, [addLog, isDialogOpen, setDialogOpen, setActiveTab]);

  // Function to setup SSE connection
  const setupSSEConnection = useCallback(
    (correlationId: string) => {
      // Close any existing connection and clean up
      if (sseClientRef.current) {
        console.log("Closing existing SSE connection before setting up a new one");
        sseClientRef.current.close();
        sseClientRef.current = null;
      }

      console.log(`Setting up new SSE connection for correlation ID: ${correlationId}`);

      // Create a new SSE client
      const sseClient = new SSEClient(correlationId, {
        onOpen: () => {
          console.log("SSE connection opened");
          setIsStreaming(true);
          addLog("Connected to execution stream...");
        },
        onMessage: (event) => {
          console.log("SSE message received:", event);

          // Parse the data if it's a string
          let parsedData;
          try {
            if (typeof event.data === "string") {
              parsedData = JSON.parse(event.data);
            }
          } catch (e) {
            console.error("Error parsing SSE message data:", e);
          }

          // Handle different event types
          if (event.type === "connected") {
            addLog(`Stream connected: ${event.data}`);
          } else if (event.type === "log") {
            addLog(event.data);
          } else if (event.type === "warning") {
            addLog(`⚠️ ${event.data}`);
          } else if (event.type === "error") {
            addLog(`❌ ${event.data}`);
          } else if (event.type === "info") {
            addLog(`ℹ️ ${event.data}`);
          } else if (event.type === "success") {
            addLog(`✅ ${event.data}`);
          } else if (event.type === "completed") {
            addLog(`✓ ${event.data}`);
            setIsExecuting(false);
          } else {
            // Default message handling
            // Skip empty objects which are likely keep-alive messages
            if (event.data !== "{}" && !(parsedData && Object.keys(parsedData).length === 0)) {
              addLog(event.data);
            }

            // Check for workflow status in the parsed data
            if (parsedData && parsedData.workflow_status) {
              // Log the workflow status
              const status = parsedData.workflow_status.toLowerCase();
              if (status === "failed") {
                addLog(`❌ Workflow execution failed`);
              } else if (status === "completed") {
                addLog(`✅ Workflow execution completed successfully`);
              } else if (status === "cancelled") {
                addLog(`⚠️ Workflow execution was cancelled`);
              }

              // Handle waiting_for_approval status
              if (status === "waiting_for_approval") {
                addLog(
                  `⏸️ Workflow is waiting for approval. Node: ${parsedData.node_name || parsedData.node_id || "Unknown"}`,
                );
              }

              // If it's a terminal status, update the execution state
              if (status === "failed" || status === "completed" || status === "cancelled") {
                // Allow the user to run the workflow again after a terminal status
                // The SSE client will close the connection and set isStreaming to false
                // We'll keep isExecuting true to show the status, but allow rerunning
                console.log(
                  `Received terminal workflow status: ${status}. Workflow execution is completed.`,
                );
              }
            }
          }
        },
        onError: (error) => {
          console.error("SSE connection error:", error);
          setIsStreaming(false);
          addLog("❌ Error in execution stream connection");
        },
        onClose: (wasError) => {
          console.log("SSE connection closed", wasError ? "due to an error" : "");
          setIsStreaming(false);

          // If the connection was closed after receiving a terminal status,
          // we should allow the user to run the workflow again
          if (sseClientRef.current?.getReadyState() === EventSource.CLOSED) {
            console.log("SSE connection is fully closed, enabling Run Again button");
            // We don't set isExecuting to false completely to keep showing the status,
            // but we need to update the UI to allow rerunning
            // This will be handled by the disabled condition in the button
          }
        },
      });

      // Store the client in the ref
      sseClientRef.current = sseClient;

      // Connect to the SSE stream
      sseClient.connect();
    },
    [addLog, setIsExecuting, setIsStreaming],
  );

  // Execute workflow with provided field values
  const executeWorkflow = useCallback(async () => {
    // Set executing state
    setIsExecuting(true);

    // Add log message
    addLog("Executing workflow with provided values...");

    try {
      // Process field values from the store - these are the values the user has edited in the form
      const fieldValues = processFieldValues();

      console.log("Sending execution request with field values:", fieldValues);
      addLog(`Processing ${Object.keys(fieldValues).length} node configurations for execution`);

      // Log each node's values for debugging
      Object.entries(fieldValues).forEach(([nodeId, values]) => {
        console.log(`Node ${nodeId} values:`, values);
        addLog(`Node ${nodeId} configured with ${Object.keys(values).length} parameters`);
      });

      // Use workflow_id from props or fallback to URL params for backward compatibility
      const workflow_id =
        workflowId ||
        (() => {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get("workflow_id");
        })();
      console.log("Using workflow_id:", workflow_id);

      // Store the current field values in the StartNode's collected parameters
      // This ensures they're available for review in the Start node and for future executions
      if (typeof window !== "undefined") {
        if (!window.startNodeCollectedParameters) {
          window.startNodeCollectedParameters = {};
        }

        console.log("Storing field values in StartNode collected parameters");
        Object.keys(fieldValues).forEach((fieldId) => {
          // Extract node_id and field_name from the fieldId
          const [nodeId, ...fieldNameParts] = fieldId.split("_");
          const fieldName = fieldNameParts.join("_");

          // Get the node name from the nodes array
          const node = nodes.find((n) => n.id === nodeId);
          const nodeName = node ? node.data.label || "Unknown Node" : "Unknown Node";

          // Get the node definition to check if the field is required
          const nodeDefinition = node?.data?.definition;
          const inputDef = nodeDefinition?.inputs?.find((input) => input.name === fieldName);

          // Determine if the field is required - consider it required unless explicitly marked as optional
          const isRequired = inputDef ? inputDef.required !== false : true;

          // Store with full metadata
          // Use type assertion to avoid TypeScript errors
          ((window as any).startNodeCollectedParameters || {})[fieldId] = {
            node_id: nodeId,
            node_name: nodeName,
            input_name: fieldName,
            value: fieldValues[fieldId],
            connected_to_start: false, // We don't know this here, but it's not critical
            required: isRequired, // Explicitly set the required property
            input_type: inputDef?.input_type || typeof fieldValues[fieldId],
          };
        });
        console.log(
          "Updated startNodeCollectedParameters:",
          (window as any).startNodeCollectedParameters,
        );
      }

      // Execute the workflow with the latest values from the form
      const result = await executeWorkflowWithValues(
        nodes,
        edges,
        fieldValues,
        workflow_id || undefined,
      );

      if (result.success) {
        // Check if we have a correlation ID for streaming
        if (result.correlation_id) {
          // Store the correlation ID
          setCorrelationId(result.correlation_id);

          // Setup SSE connection
          setupSSEConnection(result.correlation_id);

          // Add log message about streaming
          addLog(
            `Workflow execution initiated. Streaming logs with correlation ID: ${result.correlation_id}`,
          );
        } else {
          // No correlation ID, just show success message
          addLog("Workflow executed successfully.");
          setIsExecuting(false);

          // Add any results to logs
          if (result.results) {
            addLog(`Results: ${JSON.stringify(result.results, null, 2)}`);
          }
        }
      } else {
        // Add error message to logs
        addLog(`Execution failed: ${result.error || "Unknown error"}`);
        setIsExecuting(false);
      }
    } catch (error) {
      // Log any errors
      addLog(`Error executing workflow: ${error instanceof Error ? error.message : String(error)}`);
      setIsExecuting(false);
    }
  }, [
    nodes,
    edges,
    addLog,
    setIsExecuting,
    processFieldValues,
    setCorrelationId,
    setupSSEConnection,
  ]);

  // Handle run button click
  const handleRun = useCallback(async () => {
    // Reset logs and state
    clearLogs();
    addLog("Starting workflow validation...");
    setIsExecuting(false);

    // Close any existing SSE connection and properly clean up
    if (sseClientRef.current) {
      console.log("Closing existing SSE connection before starting new workflow run");
      sseClientRef.current.close();
      sseClientRef.current = null;
    }

    // Reset correlation ID and streaming state
    setCorrelationId(null);
    setIsStreaming(false);

    // Clear all approval events to ensure a fresh start
    clearAllApprovalEvents();

    // Reset missing fields to ensure a fresh scan
    setMissingFields([]);

    // Clear any cached parameters to ensure a fresh scan
    if (typeof window !== "undefined") {
      // Don't completely clear startNodeCollectedParameters as it might contain valuable data
      // But mark it for refresh by setting a flag
      if (window.startNodeCollectedParameters) {
        window.startNodeCollectedParametersNeedsRefresh = true;
        console.log("Marked startNodeCollectedParameters for refresh");
      }
    }

    try {
      // Get the current nodes and edges from React Flow
      let currentNodes = getNodes();
      let currentEdges = getEdges();

      // Create a timestamp function for consistent logging
      const getTimestamp = () => new Date().toISOString().replace("T", " ").substring(0, 19);
      let timestamp = getTimestamp();

      // Enhanced debugging for React Flow state
      console.log(`[${timestamp}] [RunButton] ========== REACT FLOW STATE DEBUGGING ==========`);
      console.log(`[${timestamp}] [RunButton] React Flow nodes count: ${currentNodes.length}`);
      console.log(`[${timestamp}] [RunButton] React Flow edges count: ${currentEdges.length}`);
      console.log(`[${timestamp}] [RunButton] Props nodes count: ${nodes.length}`);
      console.log(`[${timestamp}] [RunButton] Props edges count: ${edges.length}`);

      // If React Flow state is empty but props have nodes, use the props instead
      if (currentNodes.length === 0 && nodes.length > 0) {
        console.warn(
          `[${timestamp}] [RunButton] WARNING: React Flow state is empty but props have nodes. Using props nodes instead.`,
        );
        currentNodes = nodes;
        currentEdges = edges;
        console.log(
          `[${timestamp}] [RunButton] Using props nodes (${currentNodes.length}) and edges (${currentEdges.length})`,
        );
      } else if (currentNodes.length === 0) {
        console.warn(
          `[${timestamp}] [RunButton] WARNING: No nodes found in React Flow state or props. This may indicate a synchronization issue.`,
        );
        addLog(
          "Warning: No nodes found in the workflow. The workflow might not be fully initialized.",
        );
      }

      // Detailed node information for debugging
      console.log(`[${timestamp}] [RunButton] Detailed node information:`);
      currentNodes.forEach((node, index) => {
        console.log(`[${timestamp}] [RunButton] Node ${index + 1}:`, {
          id: node.id,
          type: node.type,
          position: node.position,
          dataType: node.data?.type,
          originalType: node.data?.originalType,
          definitionName: node.data?.definition?.name,
          label: node.data?.label,
          hasConfig: !!node.data?.config,
          configKeys: node.data?.config ? Object.keys(node.data.config) : [],
        });
      });

      // Check for StartNode using multiple detection methods
      console.log(`[${timestamp}] [RunButton] ========== SEARCHING FOR START NODE ==========`);

      // Method 1: Check by originalType (primary method)
      const startNodeByOriginalType = currentNodes.find(
        (node: Node<WorkflowNodeData>) => node.data && node.data.originalType === "StartNode",
      );

      // Method 2: Check by definition name
      const startNodeByDefinition = currentNodes.find(
        (node: Node<WorkflowNodeData>) =>
          node.data && node.data.definition && node.data.definition.name === "StartNode",
      );

      // Method 3: Check by node type
      const startNodeByType = currentNodes.find(
        (node: Node<WorkflowNodeData>) =>
          node.type === "StartNode" || (node.data && node.data.type === "StartNode"),
      );

      // Method 4: Check by label (as a fallback)
      const startNodeByLabel = currentNodes.find(
        (node: Node<WorkflowNodeData>) =>
          node.data && (node.data.label === "Start" || node.data.label === "StartNode"),
      );

      // Method 5: Check by component type and name pattern
      const startNodeByPattern = currentNodes.find(
        (node: Node<WorkflowNodeData>) =>
          node.data &&
          node.data.type === "component" &&
          node.data.definition &&
          node.data.definition.name &&
          node.data.definition.name.toLowerCase().includes("start"),
      );

      console.log(`[${timestamp}] [RunButton] Start node detection results:`);
      console.log(
        `[${timestamp}] [RunButton] - By originalType: ${startNodeByOriginalType ? startNodeByOriginalType.id : "Not found"}`,
      );
      console.log(
        `[${timestamp}] [RunButton] - By definition name: ${startNodeByDefinition ? startNodeByDefinition.id : "Not found"}`,
      );
      console.log(
        `[${timestamp}] [RunButton] - By node type: ${startNodeByType ? startNodeByType.id : "Not found"}`,
      );
      console.log(
        `[${timestamp}] [RunButton] - By label: ${startNodeByLabel ? startNodeByLabel.id : "Not found"}`,
      );
      console.log(
        `[${timestamp}] [RunButton] - By pattern: ${startNodeByPattern ? startNodeByPattern.id : "Not found"}`,
      );

      // Use the first found StartNode from any method
      const startNode =
        startNodeByOriginalType ||
        startNodeByDefinition ||
        startNodeByType ||
        startNodeByLabel ||
        startNodeByPattern;

      if (startNode) {
        console.log(`[${timestamp}] [RunButton] Found StartNode with ID: ${startNode.id}`);

        // Check if we need to refresh the parameters
        const needsRefresh = window.startNodeCollectedParametersNeedsRefresh === true;

        if (startNode.data && startNode.data.config && startNode.data.config.collected_parameters) {
          console.log(
            `[${timestamp}] [RunButton] StartNode has collected parameters:`,
            startNode.data.config.collected_parameters,
          );

          // Store the StartNode's collected parameters in the window object
          // This will make them available to the ExecutionDialog component
          if (needsRefresh) {
            console.log(
              `[${timestamp}] [RunButton] Refreshing StartNode collected parameters due to refresh flag`,
            );
            // Clear the refresh flag
            window.startNodeCollectedParametersNeedsRefresh = false;
          }

          window.startNodeCollectedParameters = startNode.data.config.collected_parameters;
          console.log(
            `[${timestamp}] [RunButton] Stored StartNode collected parameters in window object:`,
            window.startNodeCollectedParameters,
          );
        } else {
          console.log(`[${timestamp}] [RunButton] StartNode found but has no collected parameters`);
          // Initialize empty parameters object
          window.startNodeCollectedParameters = {};

          // Clear the refresh flag if it was set
          if (needsRefresh) {
            window.startNodeCollectedParametersNeedsRefresh = false;
            console.log(`[${timestamp}] [RunButton] Cleared refresh flag`);
          }
        }
      } else {
        console.warn(`[${timestamp}] [RunButton] No StartNode found using any detection method`);
        // Clear any previously stored parameters
        window.startNodeCollectedParameters = {};
      }

      // Validate the workflow using our frontend validation
      // Update timestamp for validation logs
      timestamp = getTimestamp();
      console.log(`[${timestamp}] [RunButton] ========== STARTING WORKFLOW VALIDATION ==========`);
      console.log(`[${timestamp}] [RunButton] Total nodes: ${currentNodes.length}`);
      console.log(
        `[${timestamp}] [RunButton] Node types: ${currentNodes.map((n) => n.data?.type || "undefined").join(", ")}`,
      );
      console.log(
        `[${timestamp}] [RunButton] Node IDs: ${currentNodes.map((n) => n.id).join(", ")}`,
      );

      // Run the validation with the current nodes and edges
      timestamp = getTimestamp();
      console.log(
        `[${timestamp}] [RunButton] Calling validateCurrentWorkflowBeforeExecution() with ${currentNodes.length} nodes and ${currentEdges.length} edges`,
      );
      const validationResult = await validateCurrentWorkflowBeforeExecution(
        currentNodes,
        currentEdges,
      );

      timestamp = getTimestamp();
      console.log(`[${timestamp}] [RunButton] ========== VALIDATION RESULT ==========`);
      console.log(
        `[${timestamp}] [RunButton] Is valid: ${validationResult.isValid ? "YES" : "NO"}`,
      );
      console.log(`[${timestamp}] [RunButton] Errors: ${validationResult.errors.length}`);
      console.log(`[${timestamp}] [RunButton] Warnings: ${validationResult.warnings.length}`);
      console.log(
        `[${timestamp}] [RunButton] Start node ID: ${validationResult.startNodeId || "NONE"}`,
      );
      console.log(
        `[${timestamp}] [RunButton] Connected nodes: ${validationResult.connectedNodes ? Array.from(validationResult.connectedNodes).join(", ") : "NONE"}`,
      );
      console.log(
        `[${timestamp}] [RunButton] Missing fields: ${validationResult.missingFields ? validationResult.missingFields.length : 0}`,
      );

      // If no Start node is found but there are nodes in the workflow, try to use the first node as a fallback
      if (!validationResult.startNodeId && currentNodes.length > 0) {
        timestamp = getTimestamp();
        console.warn(
          `[${timestamp}] [RunButton] No Start node found in validation result, but workflow has ${currentNodes.length} nodes. Using fallback mechanism.`,
        );

        // Create a temporary set of connected nodes with all nodes for fallback
        const tempConnectedNodes = new Set(currentNodes.map((node) => node.id));
        console.log(
          `[${timestamp}] [RunButton] Created fallback connected nodes set with ${tempConnectedNodes.size} nodes`,
        );

        // Collect missing fields from all nodes as a fallback
        console.log(
          `[${timestamp}] [RunButton] Collecting missing fields using fallback method...`,
        );
        const allMissingFields = collectMissingRequiredFields(currentNodes, tempConnectedNodes);

        if (allMissingFields.length > 0) {
          console.log(
            `[${timestamp}] [RunButton] Found ${allMissingFields.length} missing fields using fallback method`,
          );

          // Store missing fields directly in the execution store
          console.log(
            `[${timestamp}] [RunButton] Storing ${allMissingFields.length} missing fields from fallback in execution store`,
          );
          setMissingFields(allMissingFields);

          // Add a warning to the logs
          addLog(
            `Warning: No Start node found. Using fallback validation with ${allMissingFields.length} missing fields.`,
          );
        } else {
          console.log(`[${timestamp}] [RunButton] No missing fields found using fallback method`);
          setMissingFields([]);
        }
      }

      // Log detailed error information
      if (validationResult.errors.length > 0) {
        console.log(`[${timestamp}] [RunButton] Detailed validation errors:`);
        validationResult.errors.forEach((error, index) => {
          console.log(
            `[${timestamp}] [RunButton]   ${index + 1}. ${error.code}: ${error.message} (Node: ${error.nodeId || "N/A"}, Field: ${error.fieldId || "N/A"})`,
          );
        });
      }

      // Log detailed missing fields information
      if (validationResult.missingFields && validationResult.missingFields.length > 0) {
        console.log(`[${timestamp}] [RunButton] Detailed missing fields:`);
        validationResult.missingFields.forEach((field, index) => {
          console.log(
            `[${timestamp}] [RunButton]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`,
          );
        });
      }

      // We already have the nodes and edges, no need to get them again
      console.log(
        `[${timestamp}] [RunButton] Performing direct validation to check for missing fields`,
      );

      // Get all nodes connected to the start node
      const startNodeId = validationResult.startNodeId;
      const connectedNodes = validationResult.connectedNodes || new Set<string>();

      if (startNodeId && connectedNodes.size > 0) {
        console.log(`[${timestamp}] [RunButton] ========== STARTING DIRECT VALIDATION ==========`);
        console.log(`[${timestamp}] [RunButton] Start node: ${startNodeId}`);
        console.log(
          `[${timestamp}] [RunButton] Connected nodes (${connectedNodes.size}): ${Array.from(connectedNodes).join(", ")}`,
        );

        // Log information about each connected node
        currentNodes.forEach((node) => {
          if (connectedNodes.has(node.id)) {
            console.log(
              `[${timestamp}] [RunButton] Connected node details - ID: ${node.id}, Label: ${node.data.label || "Unnamed"}, Type: ${node.data.type}`,
            );
            if (node.data.config) {
              console.log(
                `[${timestamp}] [RunButton] Node ${node.id} config keys: ${Object.keys(node.data.config).join(", ") || "none"}`,
              );
            }
            if (node.data.definition && node.data.definition.inputs) {
              console.log(
                `[${timestamp}] [RunButton] Node ${node.id} has ${node.data.definition.inputs.length} inputs`,
              );
              node.data.definition.inputs.forEach((input) => {
                console.log(
                  `[${timestamp}] [RunButton] - Input: ${input.name}, Type: ${input.input_type}, Required: ${input.required === true ? "YES" : input.required === false ? "NO" : "undefined"}`,
                );
              });
            }
          }
        });

        // Collect all fields from connected nodes, passing the edges to check for connected handles
        console.log(`[${timestamp}] [RunButton] Collecting all fields from connected nodes...`);
        const allFields = collectAllFields(currentNodes, connectedNodes, currentEdges);
        console.log(
          `[${timestamp}] [RunButton] Found ${allFields.length} total fields from connected nodes`,
        );

        if (allFields.length > 0) {
          console.log(`[${timestamp}] [RunButton] All fields details:`);
          allFields.forEach((field, index) => {
            console.log(
              `[${timestamp}] [RunButton]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}, Required: ${field.required ? "YES" : "NO"}, Empty: ${field.isEmpty ? "YES" : "NO"}`,
            );
          });
        }

        // Store all fields in the StartNode's collected parameters
        if (startNode && allFields.length > 0) {
          console.log(
            `[${timestamp}] [RunButton] Storing all fields in StartNode collected parameters...`,
          );

          // Initialize the collected parameters object if it doesn't exist
          if (!startNode.data.config) {
            startNode.data.config = {};
          }
          if (!startNode.data.config.collected_parameters) {
            startNode.data.config.collected_parameters = {};
          }

          // Add all fields to the collected parameters
          allFields.forEach((field) => {
            const fieldId = `${field.nodeId}_${field.name}`;
            if (startNode.data.config && startNode.data.config.collected_parameters) {
              // Use the directly_connected_to_start property from the field
              // This ensures we correctly identify fields with direct connections from Start node
              const isDirectlyConnected = field.directly_connected_to_start === true;

              console.log(`[${timestamp}] [RunButton] Adding field ${field.nodeName}.${field.name} to StartNode collected parameters:
                - Field ID: ${fieldId}
                - Directly connected to Start: ${isDirectlyConnected ? "YES" : "NO"}
                - Required: ${field.required !== false ? "YES" : "NO"}`);

              startNode.data.config.collected_parameters[fieldId] = {
                node_id: field.nodeId,
                node_name: field.nodeName,
                input_name: field.name,
                value: field.currentValue,
                connected_to_start: isDirectlyConnected, // Use the correct value from the field
                required: field.required,
                input_type: field.inputType,
                options: field.options,
              };
            }
          });

          // Store in window object for the ExecutionDialog
          (window as any).startNodeCollectedParameters = startNode.data.config.collected_parameters;
          console.log(
            `[${timestamp}] [RunButton] Stored ${Object.keys((window as any).startNodeCollectedParameters).length} fields in window.startNodeCollectedParameters`,
          );
        }

        // Also collect missing required fields for validation
        console.log(`[${timestamp}] [RunButton] Collecting missing required fields directly...`);
        const directMissingFields = collectMissingRequiredFields(
          currentNodes,
          connectedNodes,
          currentEdges,
        );
        console.log(
          `[${timestamp}] [RunButton] Direct validation found ${directMissingFields.length} missing required fields`,
        );

        if (directMissingFields.length > 0) {
          console.log(`[${timestamp}] [RunButton] Missing required fields details:`);
          directMissingFields.forEach((field, index) => {
            console.log(
              `[${timestamp}] [RunButton]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`,
            );
          });
        }

        // Use the directly collected missing fields if available, otherwise use the ones from validation result
        const missingFields =
          directMissingFields.length > 0
            ? directMissingFields
            : validationResult.missingFields || [];

        if (missingFields.length > 0) {
          console.log(`[${timestamp}] [RunButton] ========== PROCESSING MISSING FIELDS ==========`);
          console.log(
            `[${timestamp}] [RunButton] Total missing fields to process: ${missingFields.length}`,
          );

          // Group missing fields by node for better organization
          const missingFieldsByNode: Record<string, MissingField[]> = {};
          missingFields.forEach((field: MissingField) => {
            if (!missingFieldsByNode[field.nodeId]) {
              missingFieldsByNode[field.nodeId] = [];
            }
            missingFieldsByNode[field.nodeId].push(field);
          });

          console.log(
            `[${timestamp}] [RunButton] Missing fields grouped by ${Object.keys(missingFieldsByNode).length} nodes`,
          );
          Object.entries(missingFieldsByNode).forEach(([nodeId, fields]) => {
            console.log(
              `[${timestamp}] [RunButton] Node ${nodeId} has ${fields.length} missing fields`,
            );
          });

          // Store missing fields directly in the execution store
          console.log(`[${timestamp}] [RunButton] Storing missing fields in execution store`);
          setMissingFields(missingFields);

          // Log summary of missing fields for the user
          addLog(
            `Found ${missingFields.length} missing required fields across ${Object.keys(missingFieldsByNode).length} nodes.`,
          );

          // Log each node's missing fields for debugging and user information
          Object.entries(missingFieldsByNode).forEach(([nodeId, fields]) => {
            const nodeName = fields[0].nodeName || nodeId;
            console.log(
              `[${timestamp}] [RunButton] Adding log for node ${nodeName} with ${fields.length} missing fields`,
            );
            addLog(`Node "${nodeName}" is missing ${fields.length} required fields:`);

            // Log each missing field for this node
            fields.forEach((field: MissingField) => {
              console.log(
                `[${timestamp}] [RunButton] Adding log for missing field ${field.displayName}`,
              );
              addLog(`  - ${field.displayName} (${field.inputType})`);
            });
          });

          console.log(
            `[${timestamp}] [RunButton] ========== MISSING FIELDS PROCESSING COMPLETE ==========`,
          );
        } else {
          console.log(`[${timestamp}] [RunButton] No missing fields found in direct validation`);
          // If no missing fields, set an empty array
          setMissingFields([]);
        }
      } else {
        console.log(`[${timestamp}] [RunButton] No start node or connected nodes found`);
        // If no start node or connected nodes, set an empty array
        setMissingFields([]);
      }

      // Store the current workflow nodes in the window object
      // This will be used by the ExecutionDialog to filter out fields from deleted nodes
      // For prebuilt workflows, we need to ensure we don't overwrite the nodes with an empty array
      if (currentNodes.length > 0) {
        window.currentWorkflowNodes = currentNodes;
        console.log(
          `[${timestamp}] [RunButton] Stored ${currentNodes.length} nodes in window.currentWorkflowNodes`,
        );
      } else {
        // If currentNodes is empty but we have missing fields, this might be a prebuilt workflow
        // In this case, we should not overwrite the nodes with an empty array
        if (window.currentWorkflowNodes && window.currentWorkflowNodes.length > 0) {
          console.log(
            `[${timestamp}] [RunButton] Keeping existing ${window.currentWorkflowNodes.length} nodes in window.currentWorkflowNodes for prebuilt workflow`,
          );
        } else {
          // If there are no existing nodes, initialize with an empty array
          window.currentWorkflowNodes = [];
          console.log(
            `[${timestamp}] [RunButton] Initialized empty window.currentWorkflowNodes array`,
          );
        }
      }

      // Log dialog opening
      console.log(`[${timestamp}] [RunButton] ========== OPENING EXECUTION DIALOG ==========`);
      console.log(`[${timestamp}] [RunButton] Dialog will open with tab: parameters`);
      const currentMissingFields = executionStore.missingFields;
      console.log(
        `[${timestamp}] [RunButton] Missing fields count: ${currentMissingFields ? currentMissingFields.length : 0}`,
      );
      console.log(
        `[${timestamp}] [RunButton] StartNode collected parameters: ${JSON.stringify(window.startNodeCollectedParameters || {})}`,
      );

      // Reset the execution store state to ensure fresh input values
      console.log(
        `[${timestamp}] [RunButton] Resetting execution store state to ensure fresh input values`,
      );
      resetState();

      // The ExecutionDialog component will handle adding fields from StartNode parameters
      // This avoids duplicate logic and potential infinite loops

      // Always open dialog with parameters tab first
      setDialogOpen(true);
      setActiveTab("parameters");

      // If validation failed, show simple toast messages and return
      if (!validationResult.isValid) {
        if (validationResult.errors.length > 0) {
          // Check if there are MCP authorization errors
          const mcpAuthErrors = validationResult.errors.filter(error =>
            error.fieldId === "oauth_authorization" ||
            error.message.includes("OAuth authorization") ||
            error.message.includes("requires OAuth")
          );

          if (mcpAuthErrors.length > 0) {
            // Show simple toast for MCP authorization errors
            mcpAuthErrors.forEach(error => {
              // Extract provider name from the error message for a simpler message
              const providerMatch = error.message.match(/connect to (\w+)/i);
              const provider = providerMatch ? providerMatch[1].toLowerCase() : "your mcp";

              toast.error(`First connect your mcp with ${provider}`, {
                duration: 5000,
              });
            });
          } else {
            // Show simple error message for other validation errors
            toast.error("Please fix workflow issues before running", {
              duration: 5000,
            });
          }

          // Show error message in logs
          const errorMessage = validationResult.errors.map((e) => e.message).join("; ");
          addLog(`Validation error: ${errorMessage}`);
        } else {
          // No specific error but validation failed
          toast.error("Please check your workflow configuration", {
            duration: 5000,
          });
          addLog("Validation failed. Please check your workflow configuration.");
        }
        return;
      }

      // If validation passed, we still want to show the parameters dialog first
      addLog("Validation successful. Please review parameters before execution.");

      // We'll let the user execute the workflow from the dialog
      // Don't automatically execute the workflow
      // await executeWorkflow();
    } catch (error) {
      // Handle unexpected errors
      addLog(`Error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [
    nodes,
    edges,
    addLog,
    clearLogs,
    setActiveTab,
    setIsExecuting,
    setDialogOpen,
    setMissingFields,
    setCorrelationId,
    setIsStreaming,
    resetState,
    getNodes,
    getEdges,
    validateCurrentWorkflowBeforeExecution,
    executionStore,
  ]);

  // Close dialog without stopping SSE connection
  const handleCloseDialog = () => {
    console.log("Closing dialog but keeping SSE connection active");

    // If we're streaming, set hasActiveExecution to true
    if (isStreaming) {
      setHasActiveExecution(true);
    }

    // Close the dialog but keep the SSE connection
    setDialogOpen(false);
  };

  // Stop execution, send stop request to backend, and close SSE connection
  const handleStopExecution = async () => {
    console.log("Stopping workflow execution");
    const { correlationId } = useExecutionStore.getState();

    if (correlationId) {
      try {
        console.log(`Sending stop request to backend for correlation ID: ${correlationId}`);
        // Import the sendApprovalDecision function from api.ts
        const { sendApprovalDecision } = await import("@/lib/api");

        // Send the request to stop the workflow execution with "reject" decision
        const result = await sendApprovalDecision(correlationId, "reject");

        if (result.success) {
          console.log(
            `Successfully stopped workflow execution for correlation ID: ${correlationId}`,
          );
          // Add a log entry to the execution store
          addLog(`✅ Successfully stopped workflow execution`);
        } else {
          console.error(`Failed to stop workflow execution: ${result.error}`);
          // Add an error log entry to the execution store
          addLog(`❌ Failed to stop workflow execution: ${result.error}`);
        }
      } catch (error) {
        console.error("Error stopping workflow execution:", error);
        // Add an error log entry to the execution store
        addLog(
          `❌ Error stopping workflow execution: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    } else {
      console.warn("No correlation ID available, cannot send stop request to backend");
      // Add a warning log entry to the execution store
      addLog(`⚠️ Cannot stop workflow execution: No correlation ID available`);
    }

    // Close the SSE connection
    if (sseClientRef.current) {
      console.log("Closing SSE connection on stop execution");
      sseClientRef.current.close();
      sseClientRef.current = null;
    }

    // Update the state
    stopExecution();
  };

  // Always render the dialog component, but it will internally handle visibility
  // This avoids React hooks issues with conditional rendering
  const renderDialog = () => {
    return (
      <ExecutionDialog
        onClose={handleCloseDialog}
        onStopExecution={handleStopExecution}
        workflowId={workflowId}
      />
    );
  };

  // Handle button click based on current state
  const handleButtonClick = () => {
    if (hasActiveExecution || isStreaming) {
      // If there's an active execution, view it
      viewExecution();
    } else {
      // If a custom onRun handler is provided, use it
      if (onRun) {
        onRun();
      } else {
        // Otherwise, use the default run handler
        handleRun();
      }
    }
  };

  return (
    <>
      <button
        type="button"
        onClick={handleButtonClick}
        disabled={disabled && !hasActiveExecution}
        className={`inline-flex h-8 items-center justify-center gap-1.5 rounded-md px-3 py-2 text-sm font-medium ${
          hasActiveExecution
            ? "bg-blue-600 hover:bg-blue-700"
            : isStreaming
              ? "bg-yellow-600 hover:bg-yellow-700"
              : "bg-green-600 hover:bg-green-700"
        } text-white disabled:pointer-events-none disabled:opacity-50`}
        title={
          hasActiveExecution
            ? "View ongoing workflow execution"
            : isStreaming
              ? "Workflow is running"
              : "Run workflow"
        }
      >
        {hasActiveExecution ? (
          <>
            <Eye className="h-4 w-4" />
            View Execution
          </>
        ) : isStreaming ? (
          <>
            <Play className="h-4 w-4" />
            Running...
          </>
        ) : (
          <>
            <Play className="h-4 w-4" />
            Run
          </>
        )}

        {/* Show a badge indicator if there's an active execution */}
        {hasActiveExecution && (
          <span className="absolute top-0 right-0 -mt-1 -mr-1 flex h-3 w-3">
            <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-blue-400 opacity-75"></span>
            <span className="relative inline-flex h-3 w-3 rounded-full bg-blue-500"></span>
          </span>
        )}
      </button>

      {renderDialog()}
    </>
  );
});
