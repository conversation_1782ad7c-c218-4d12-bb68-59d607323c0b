from typing import Dict, Any, List, ClassVar
import copy
import json
import time
import logging
import asyncio

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    DropdownInput,
    IntInput,
    InputVisibilityRule,
)
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import Node<PERSON><PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

# Set up logging
logger = logging.getLogger(__name__)


class MergeDataComponent(BaseNode):
    """
    Combines multiple dictionaries or lists.

    This component takes a main input and a configurable number of additional inputs
    (dictionaries or lists) and combines them according to the specified merge strategy.
    """

    name: ClassVar[str] = "MergeDataComponent"
    display_name: ClassVar[str] = "Merge Data"
    description: ClassVar[str] = "Combines multiple dictionaries or lists."
    category: ClassVar[str] = "Processing"
    icon: ClassVar[str] = "Combine"

    # Define a maximum number of additional inputs to support
    MAX_ADDITIONAL_INPUTS = 10
    DEFAULT_ADDITIONAL_INPUTS = 0

    # Generate the inputs dynamically
    inputs: ClassVar[List[InputBase]] = [
        # Main input - dual purpose input
        create_dual_purpose_input(
            name="main_input",
            display_name="Main Input",
            input_type="dict",
            required=True,
            info="The main data structure to merge. Can be connected from another node or entered directly.",
            input_types=["dict", "list", "Any"],
        ),
        # Number of additional inputs to show
        IntInput(
            name="num_additional_inputs",
            display_name="Number of Additional Inputs",
            value=DEFAULT_ADDITIONAL_INPUTS,  # Default to 2 additional inputs
            info=f"Set the number of additional inputs to show (1-{MAX_ADDITIONAL_INPUTS}).",
        ),
        DropdownInput(
            name="merge_strategy",
            display_name="Merge Strategy (Dicts)",
            options=["Overwrite", "Deep Merge", "Error on Conflict", "Aggregate", "Structured Compose"],
            value="Overwrite",
            info="How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs.",
        ),
    ]

    # Add dynamic output key inputs for Structured Compose strategy
    # We need output keys for: main_input + all additional inputs (up to MAX_ADDITIONAL_INPUTS)
    # Note: Visibility logic is handled entirely in the frontend (inputVisibility.ts)
    for i in range(1, MAX_ADDITIONAL_INPUTS + 2):  # +2 because we need keys for main_input + all additional inputs
        # Add output key input (no visibility rules - frontend handles all visibility logic)
        inputs.append(
            create_dual_purpose_input(
                name=f"output_key_{i}",
                display_name=f"Output Key {i}",
                input_type="string",
                required=False,
                info=f"Custom key name for input {i} (e.g., 'data_{i}'). Only used with Structured Compose strategy.",
                input_types=["string"],
                # No visibility_rules - frontend handles complex visibility logic
            )
        )

    # Add additional input fields dynamically
    for i in range(1, MAX_ADDITIONAL_INPUTS + 1):
        # Create visibility rules - show if num_additional_inputs is >= i
        # Since we don't have a "greater than or equal" operator, we create a rule for each possible value
        visibility_rules = [
            InputVisibilityRule(field_name="num_additional_inputs", field_value=j)
            for j in range(i, MAX_ADDITIONAL_INPUTS + 1)
        ]

        # Add dual purpose input
        inputs.append(
            create_dual_purpose_input(
                name=f"input_{i}",
                display_name=f"Input {i}",
                input_type="dict",
                required=False,
                info=f"Data structure {i} to merge. Can be connected from another node or entered directly.",
                input_types=["dict", "list", "Any"],
                visibility_rules=visibility_rules,
            )
        )

    outputs: ClassVar[List[Output]] = [
        Output(name="output_data", display_name="Merged Data", output_type="Any"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    def _deep_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively merges two dictionaries.

        Args:
            dict1: The first dictionary
            dict2: The second dictionary

        Returns:
            The merged dictionary
        """
        result = copy.deepcopy(dict1)

        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # Recursively merge nested dictionaries
                result[key] = self._deep_merge(result[key], value)
            else:
                # Otherwise, simply overwrite or add the value
                result[key] = copy.deepcopy(value)

        return result

    def _aggregate_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merges two dictionaries by aggregating values for common keys.
        If a key exists in both, the values are collected into a list.
        """
        result = copy.deepcopy(dict1)

        for key, value in dict2.items():
            if key in result:
                # If key exists, aggregate values
                if isinstance(result[key], list):
                    # If existing value is a list, append or extend
                    if isinstance(value, list):
                        result[key].extend(copy.deepcopy(value))
                    else:
                        result[key].append(copy.deepcopy(value))
                else:
                    # If existing value is not a list, create a new list
                    result[key] = [result[key], copy.deepcopy(value)]
            else:
                # If key does not exist, just add it
                result[key] = copy.deepcopy(value)

        return result

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the context.

        This method handles dual-purpose inputs, retrieving the value from the context.

        Args:
            input_name: The name of the input.
            context: The workflow execution context.
            default: The default value to return if the input is not found.

        Returns:
            The value of the input, or the default value if not found.
        """
        # Get the current node ID from the context
        node_id = context.current_node_id
        if not node_id:
            logger.warning("No current node ID in context")
            return default

        # Check if there's a value in the node outputs
        node_outputs = context.node_outputs.get(node_id, {})
        if input_name in node_outputs:
            return node_outputs[input_name]

        # If not found, return the default
        return default

    def get_connected_output_key(self, input_name: str, context: WorkflowContext) -> str:
        """
        Get the output key name from the connected node for this input.

        This method looks at the workflow edges to find which node output
        is connected to this input, and returns the output handle name.

        Args:
            input_name: The name of the input (e.g., "main_input", "input_1")
            context: The workflow execution context

        Returns:
            The output handle name from the connected node, or None if not found
        """
        try:
            current_node_id = context.current_node_id
            if not current_node_id:
                return None

            # Look through workflow edges to find connections to this input
            workflow = context.workflow
            if not workflow or not hasattr(workflow, 'edges'):
                return None

            for edge in workflow.edges:
                # Check if this edge connects to our current node's input
                if (edge.target == current_node_id and
                    edge.target_handle == input_name):

                    # Found the connection - return the source output handle name
                    source_handle = edge.source_handle
                    logger.debug(f"Found connection: {edge.source}:{source_handle} -> {current_node_id}:{input_name}")
                    return source_handle

        except Exception as e:
            logger.warning(f"Error getting connected output key for {input_name}: {str(e)}")

        return None

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the MergeDataComponent.

        This method merges multiple data structures (lists or dictionaries) according to
        the specified merge strategy.

        Args:
            context: The workflow execution context containing input values.

        Returns:
            A NodeResult with the execution results.
        """
        # Start timing for performance measurement
        start_time = time.time()

        # Log execution start
        context.log(f"Executing {self.name}...")

        try:
            # Get inputs from context
            main_input = self.get_input_value("main_input", context)
            num_additional_inputs = min(
                int(self.get_input_value("num_additional_inputs", context, self.DEFAULT_ADDITIONAL_INPUTS)),
                self.MAX_ADDITIONAL_INPUTS
            )
            merge_strategy = self.get_input_value("merge_strategy", context, "Overwrite")

            # Log input values for debugging
            logger.debug(f"Main input type: {type(main_input).__name__}")
            logger.debug(f"Number of additional inputs: {num_additional_inputs}")
            logger.debug(f"Merge strategy: {merge_strategy}")

            # Validate main input
            if main_input is None:
                error_msg = "Main input is missing. Please connect data to merge."
                context.log(f"  {error_msg}")
                return NodeResult.error(error_msg, time.time() - start_time)

            # Prepare the tool parameters for the node-executor-service
            tool_parameters = {
                "main_input": main_input,
                "num_additional_inputs": num_additional_inputs,
                "merge_strategy": merge_strategy,
                "output_key_1": self.get_input_value("output_key_1", context, "data_1"),
                "output_key_2": self.get_input_value("output_key_2", context, "data_2")
            }

            # Add additional inputs to the parameters
            for i in range(1, self.MAX_ADDITIONAL_INPUTS + 1):
                input_name = f"input_{i}"
                if i <= num_additional_inputs:
                    input_value = self.get_input_value(input_name, context)
                    tool_parameters[input_name] = input_value
                else:
                    # Include all possible inputs with null values for ones not used
                    tool_parameters[input_name] = None

            # Log the tool parameters
            logger.debug(f"Tool parameters: {tool_parameters}")

            # In a production environment, this would call the node-executor-service
            # via the orchestration engine. However, since we don't have direct access
            # to the orchestration engine in this context, we'll implement the logic directly.

            # For now, we'll use local execution
            context.log("  Using local execution for merge operation")
            logger.info("Using local execution for merge operation")

            # Handle Structured Compose strategy differently
            if merge_strategy == "Structured Compose":
                # For structured compose, create a new dictionary with custom keys
                result = {}

                # Get custom key names
                output_key_1 = self.get_input_value("output_key_1", context, "data_1")
                output_key_2 = self.get_input_value("output_key_2", context, "data_2")

                # Add main input with first key
                result[output_key_1] = copy.deepcopy(main_input)
                context.log(f"  Added main input as '{output_key_1}'")

                # Add additional inputs with their respective keys
                for i in range(1, min(num_additional_inputs + 1, 3)):  # Limit to 2 additional for now
                    input_name = f"input_{i}"
                    input_value = self.get_input_value(input_name, context)

                    if input_value is not None:
                        if i == 1:
                            result[output_key_2] = copy.deepcopy(input_value)
                            context.log(f"  Added {input_name} as '{output_key_2}'")
                        else:
                            # For additional inputs beyond 2, use generic keys
                            key_name = f"data_{i + 1}"
                            result[key_name] = copy.deepcopy(input_value)
                            context.log(f"  Added {input_name} as '{key_name}'")

                context.log(f"  Structured compose completed with keys: {list(result.keys())}")
                return NodeResult.success(
                    {"output_data": result},
                    time.time() - start_time
                )

            # Initialize the result with the main input for other strategies
            result = copy.deepcopy(main_input)
            input_type = type(main_input)

            # Process additional inputs
            for i in range(1, num_additional_inputs + 1):
                input_name = f"input_{i}"
                input_value = self.get_input_value(input_name, context)

                # Skip None or empty inputs
                if input_value is None:
                    logger.debug(f"Skipping None input for {input_name}")
                    continue

                # Check if types are compatible
                if not isinstance(input_value, input_type):
                    if isinstance(main_input, list) and isinstance(input_value, list):
                        # Both are lists, so it's fine
                        pass
                    elif isinstance(main_input, dict) and isinstance(input_value, dict):
                        # Both are dicts, so it's fine
                        pass
                    else:
                        # Types are not compatible
                        error_msg = (
                            f"Cannot merge data of types {type(result).__name__} and {type(input_value).__name__} "
                            f"for input {input_name}. All inputs must be of the same type (list or dict)."
                        )
                        context.log(f"  {error_msg}")
                        return NodeResult.error(error_msg, time.time() - start_time)

                # Merge based on type
                if isinstance(result, list) and isinstance(input_value, list):
                    # Merge lists by concatenation
                    try:
                        result = result + input_value
                        context.log(f"  List {input_name} merged successfully. Current length: {len(result)}")
                    except Exception as e:
                        error_msg = f"Error merging list {input_name}: {str(e)}"
                        context.log(f"  {error_msg}")
                        return NodeResult.error(error_msg, time.time() - start_time)

                elif isinstance(result, dict) and isinstance(input_value, dict):
                    # Merge dictionaries based on strategy
                    try:
                        if merge_strategy == "Overwrite":
                            # Shallow merge (update with new dict)
                            result.update(copy.deepcopy(input_value))
                            context.log(
                                f"  Dictionary {input_name} merged with overwrite strategy. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Deep Merge":
                            # Deep merge (recursively merge nested dictionaries)
                            result = self._deep_merge(result, input_value)
                            context.log(
                                f"  Dictionary {input_name} merged with deep merge strategy. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Aggregate":
                            # Aggregate merge (combine conflicting values into a list)
                            result = self._aggregate_merge(result, input_value)
                            context.log(
                                f"  Dictionary {input_name} merged with aggregate strategy. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Error on Conflict":
                            # Check for conflicts
                            conflicts = [key for key in input_value if key in result]
                            if conflicts:
                                error_msg = f"Key conflicts detected with {input_name}: {conflicts}"
                                context.log(f"  {error_msg}")
                                return NodeResult.error(error_msg, time.time() - start_time)

                            # No conflicts, safe to merge
                            result.update(copy.deepcopy(input_value))
                            context.log(f"  Dictionary {input_name} merged with no conflicts. Current keys: {list(result.keys())}")

                        else:
                            error_msg = f"Unknown merge strategy: {merge_strategy}"
                            context.log(f"  {error_msg}")
                            return NodeResult.error(error_msg, time.time() - start_time)

                    except Exception as e:
                        error_msg = f"Error merging dictionary {input_name}: {str(e)}"
                        context.log(f"  {error_msg}")
                        return NodeResult.error(error_msg, time.time() - start_time)

            # Return the final merged result
            context.log(f"  All data merged successfully.")
            return NodeResult.success(
                {"output_data": result},
                time.time() - start_time
            )

        except Exception as e:
            error_msg = f"Error executing {self.name}: {str(e)}"
            context.log(f"  {error_msg}")
            return NodeResult.error(error_msg, time.time() - start_time)

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the MergeDataComponent.

        This method is deprecated and will be replaced by the execute method.
        It is kept for backward compatibility.

        Args:
            **kwargs: Contains the input values:
                - main_input: The main data structure
                - input_1, input_2, etc.: Additional data structures
                - num_additional_inputs: Number of additional inputs to process
                - merge_strategy: How to handle conflicts

        Returns:
            A dictionary with either:
                - output_data: The merged data
                - error: An error message if the operation failed
        """
        logger.warning(f"Using legacy build method for {self.name}. Please update to use execute method.")

        # Get inputs
        main_input = kwargs.get("main_input")
        num_additional_inputs = min(
            int(kwargs.get("num_additional_inputs", self.DEFAULT_ADDITIONAL_INPUTS)),
            self.MAX_ADDITIONAL_INPUTS
        )
        merge_strategy = kwargs.get("merge_strategy", "Overwrite")

        # Validate main input
        if main_input is None:
            return {"error": "Main input is missing. Please connect data to merge."}

        try:
            # Handle Structured Compose strategy differently
            if merge_strategy == "Structured Compose":
                # For structured compose, create a new dictionary with custom keys
                result = {}

                # Get custom key names
                output_key_1 = kwargs.get("output_key_1", "data_1")
                output_key_2 = kwargs.get("output_key_2", "data_2")

                # Add main input with first key
                result[output_key_1] = copy.deepcopy(main_input)
                print(f"  Added main input as '{output_key_1}'")

                # Add additional inputs with their respective keys
                for i in range(1, min(num_additional_inputs + 1, 3)):  # Limit to 2 additional for now
                    input_name = f"input_{i}"
                    input_value = kwargs.get(input_name)

                    if input_value is not None:
                        if i == 1:
                            result[output_key_2] = copy.deepcopy(input_value)
                            print(f"  Added {input_name} as '{output_key_2}'")
                        else:
                            # For additional inputs beyond 2, use generic keys
                            key_name = f"data_{i + 1}"
                            result[key_name] = copy.deepcopy(input_value)
                            print(f"  Added {input_name} as '{key_name}'")

                print(f"  Structured compose completed with keys: {list(result.keys())}")
                return {"output_data": result}

            # Initialize the result with the main input for other strategies
            result = copy.deepcopy(main_input)
            input_type = type(main_input)

            # Process additional inputs
            for i in range(1, num_additional_inputs + 1):
                input_name = f"input_{i}"
                input_value = kwargs.get(input_name)

                # Skip None or empty inputs
                if input_value is None:
                    continue

                # Check if types are compatible
                if not isinstance(input_value, input_type):
                    if isinstance(main_input, list) and isinstance(input_value, list):
                        # Both are lists, so it's fine
                        pass
                    elif isinstance(main_input, dict) and isinstance(input_value, dict):
                        # Both are dicts, so it's fine
                        pass
                    else:
                        # Types are not compatible
                        return {
                            "error": f"Cannot merge data of types {type(result).__name__} and {type(input_value).__name__} "
                                    f"for input {input_name}. All inputs must be of the same type (list or dict)."
                        }

                # Merge based on type
                if isinstance(result, list) and isinstance(input_value, list):
                    # Merge lists by concatenation
                    try:
                        result = result + input_value
                        print(f"  List {input_name} merged successfully. Current length: {len(result)}")
                    except Exception as e:
                        error_msg = f"Error merging list {input_name}: {str(e)}"
                        print(f"  {error_msg}")
                        return {"error": error_msg}

                elif isinstance(result, dict) and isinstance(input_value, dict):
                    # Merge dictionaries based on strategy
                    try:
                        if merge_strategy == "Overwrite":
                            # Shallow merge (update with new dict)
                            result.update(copy.deepcopy(input_value))
                            print(
                                f"  Dictionary {input_name} merged with overwrite strategy. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Deep Merge":
                            # Deep merge (recursively merge nested dictionaries)
                            result = self._deep_merge(result, input_value)
                            print(
                                f"  Dictionary {input_name} merged with deep merge strategy. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Aggregate":
                            # Aggregate merge (combine conflicting values into a list)
                            result = self._aggregate_merge(result, input_value)
                            print(
                                f"  Dictionary {input_name} merged with aggregate strategy. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Error on Conflict":
                            # Check for conflicts
                            conflicts = [key for key in input_value if key in result]
                            if conflicts:
                                return {"error": f"Key conflicts detected with {input_name}: {conflicts}"}

                            # No conflicts, safe to merge
                            result.update(copy.deepcopy(input_value))
                            print(f"  Dictionary {input_name} merged with no conflicts. Current keys: {list(result.keys())}")

                        else:
                            return {"error": f"Unknown merge strategy: {merge_strategy}"}

                    except Exception as e:
                        error_msg = f"Error merging dictionary {input_name}: {str(e)}"
                        print(f"  {error_msg}")
                        return {"error": error_msg}

            # Return the final merged result
            print(f"  All data merged successfully.")
            return {"output_data": result}

        except Exception as e:
            error_msg = f"Error in build method: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
