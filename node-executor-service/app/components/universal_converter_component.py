"""
Universal Converter Component - Converts data between different types.
"""
import logging
import traceback
import json
import csv
import io
import re
from typing import Dict, List, Any, Union

try:
    from app.core_.base_component import BaseComponent, ValidationResult
    from app.core_.component_system import register_component
except ImportError as e:
    print(f"CRITICAL: Failed to import core components for UniversalConverterComponent: {e}")
    raise

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("UniversalConverterComponent")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


@register_component("UniversalConverterComponent")
class UniversalConverterExecutor(BaseComponent):
    """
    Component for converting data between different types.

    This component can convert between JSON, CSV, strings, numbers, booleans,
    and other common data formats used in workflow automation.
    """

    def __init__(self):
        """
        Initialize the UniversalConverterExecutor component.
        """
        super().__init__()
        logger.info("UniversalConverterExecutor initialized")

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a universal converter payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.debug(f"Validating universal converter payload for request_id: {request_id}")

        try:
            # Extract parameters the same way as process method
            if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
                logger.debug(f"Found 'tool_parameters' field in payload for validation.")
                parameters = payload["tool_parameters"]
            else:
                parameters = payload

            # Check for required fields in the correct parameter location
            if "input_data" not in parameters:
                error_msg = f"Missing required field 'input_data' in parameters for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"input_data": "required field missing"})

            if "to_type" not in parameters:
                error_msg = f"Missing required field 'to_type' in parameters for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"to_type": "required field missing"})

            # Validate to_type if present
            to_type = parameters.get("to_type")
            valid_types = [
                "String", "Number", "Boolean", "Object", "Array", 
                "JSON String", "CSV String", "Joined String", "Split Array", "Flattened Object"
            ]
            if to_type not in valid_types:
                error_msg = f"Invalid to_type: {to_type}. Must be one of: {', '.join(valid_types)} for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"to_type": f"invalid value: {to_type}"})

            # Validate from_type if present
            from_type = parameters.get("from_type")
            if from_type is not None:
                valid_from_types = ["Auto-detect", "String", "Number", "Boolean", "Object", "Array", "Null"]
                if from_type not in valid_from_types:
                    error_msg = f"Invalid from_type: {from_type}. Must be one of: {', '.join(valid_from_types)} for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(is_valid=False, error_message=error_msg, error_details={"from_type": f"invalid value: {from_type}"})

            logger.debug(f"Universal converter payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg, error_details={"validation_error": str(e)})

    def _detect_type(self, data: Any) -> str:
        """
        Auto-detect the type of input data.

        Args:
            data: The data to analyze

        Returns:
            String representing the detected type
        """
        if data is None:
            return "Null"
        elif isinstance(data, bool):
            return "Boolean"
        elif isinstance(data, int):
            return "Number"
        elif isinstance(data, float):
            return "Number"
        elif isinstance(data, str):
            return "String"
        elif isinstance(data, list):
            return "Array"
        elif isinstance(data, dict):
            return "Object"
        else:
            return "String"  # Default fallback

    def _safe_json_parse(self, json_str: str) -> Any:
        """
        Safely parse a JSON string with error handling and support for multi-level encoding.

        Args:
            json_str: The JSON string to parse

        Returns:
            Parsed JSON data

        Raises:
            ValueError: If the JSON cannot be parsed
        """
        # Clean markdown code blocks if present
        cleaned_str = self._clean_markdown_json(json_str)

        try:
            # First parse attempt
            parsed = json.loads(cleaned_str)

            # Check if the result is still a JSON string (double-encoded)
            if isinstance(parsed, str) and self._looks_like_json(parsed):
                logger.debug("Detected double-encoded JSON, parsing again")
                # Try to parse again for double-encoded JSON
                try:
                    parsed = json.loads(parsed)
                    logger.debug(f"Successfully parsed double-encoded JSON, result type: {type(parsed).__name__}")
                except json.JSONDecodeError:
                    logger.debug("Second parse failed, returning first parse result")
                    pass  # Keep the first parse result

            return parsed

        except json.JSONDecodeError as e:
            # Try to fix common JSON issues
            try:
                # Remove trailing commas
                fixed_str = re.sub(r',(\s*[}\]])', r'\1', cleaned_str)
                # Replace single quotes with double quotes
                fixed_str = re.sub(r"'([^']*)'", r'"\1"', fixed_str)
                return json.loads(fixed_str)
            except json.JSONDecodeError:
                raise ValueError(f"Invalid JSON format: {str(e)}")

    def _clean_markdown_json(self, json_str: str) -> str:
        """
        Clean markdown code block formatting from JSON string.

        Args:
            json_str: The JSON string that may contain markdown formatting

        Returns:
            Cleaned JSON string without markdown formatting
        """
        if not isinstance(json_str, str):
            return json_str

        # Strip whitespace
        cleaned = json_str.strip()

        # Remove markdown code block markers
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]  # Remove ```json
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]  # Remove ```

        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]  # Remove trailing ```

        # Strip whitespace again after removing markers
        return cleaned.strip()

    def _looks_like_json(self, value: str) -> bool:
        """
        Check if a string looks like it could be JSON.

        Args:
            value: The string to check

        Returns:
            True if the string looks like JSON, False otherwise
        """
        if not isinstance(value, str):
            return False

        # Remove leading/trailing whitespace
        value = value.strip()

        # Check for standard JSON patterns
        return ((value.startswith('{') and value.endswith('}')) or 
                (value.startswith('[') and value.endswith(']')) or
                (value.startswith('"') and value.endswith('"') and len(value) > 1))

    def _string_to_number(self, data: str) -> Union[int, float]:
        """
        Convert string to number (int or float).

        Args:
            data: String to convert

        Returns:
            Converted number

        Raises:
            ValueError: If conversion fails
        """
        try:
            # Try integer first
            if '.' not in str(data) and 'e' not in str(data).lower():
                return int(data)
            else:
                return float(data)
        except (ValueError, TypeError):
            raise ValueError(f"Cannot convert '{data}' to number")

    def _string_to_boolean(self, data: str) -> bool:
        """
        Convert string to boolean.

        Args:
            data: String to convert

        Returns:
            Boolean value

        Raises:
            ValueError: If conversion fails
        """
        if isinstance(data, str):
            lower_data = data.lower().strip()
            if lower_data in ['true', 'yes', '1', 'on', 'y']:
                return True
            elif lower_data in ['false', 'no', '0', 'off', 'n']:
                return False
        
        raise ValueError(f"Cannot convert '{data}' to boolean")

    def _array_to_csv_string(self, data: List, delimiter: str = ",") -> str:
        """
        Convert array to CSV string.

        Args:
            data: Array to convert
            delimiter: CSV delimiter

        Returns:
            CSV string
        """
        if not isinstance(data, list):
            raise ValueError("Input must be an array for CSV conversion")

        output = io.StringIO()
        writer = csv.writer(output, delimiter=delimiter)
        
        # Handle array of arrays (rows)
        if data and isinstance(data[0], list):
            writer.writerows(data)
        else:
            # Single row
            writer.writerow(data)
        
        return output.getvalue().strip()

    def _csv_string_to_array(self, data: str, delimiter: str = ",") -> List:
        """
        Convert CSV string to array.

        Args:
            data: CSV string to parse
            delimiter: CSV delimiter

        Returns:
            Array of values or arrays
        """
        if not isinstance(data, str):
            raise ValueError("Input must be a string for CSV parsing")

        reader = csv.reader(io.StringIO(data), delimiter=delimiter)
        rows = list(reader)
        
        # If only one row, return as simple array
        if len(rows) == 1:
            return rows[0]
        
        return rows

    def _flatten_object(self, data: Dict, separator: str = ".") -> Dict:
        """
        Flatten a nested object.

        Args:
            data: Object to flatten
            separator: Key separator

        Returns:
            Flattened object
        """
        def _flatten_recursive(obj: Dict, parent_key: str = "") -> Dict:
            items = []
            for key, value in obj.items():
                new_key = f"{parent_key}{separator}{key}" if parent_key else key
                
                if isinstance(value, dict):
                    items.extend(_flatten_recursive(value, new_key).items())
                else:
                    items.append((new_key, value))
            
            return dict(items)

        if not isinstance(data, dict):
            raise ValueError("Input must be an object for flattening")

        return _flatten_recursive(data)

    def _convert_data(self, input_data: Any, from_type: str, to_type: str, delimiter: str = ",", pretty_format: bool = True) -> Any:
        """
        Convert data from one type to another.

        Args:
            input_data: The data to convert
            from_type: Source data type
            to_type: Target data type
            delimiter: Delimiter for CSV/Join/Split operations
            pretty_format: Whether to use pretty formatting for JSON

        Returns:
            Converted data

        Raises:
            ValueError: If conversion is not possible
        """
        logger.debug(f"Converting from {from_type} to {to_type}")
        
        # Handle String conversions
        if to_type == "String":
            if isinstance(input_data, str):
                return input_data
            elif isinstance(input_data, (dict, list)):
                return json.dumps(input_data, indent=2 if pretty_format else None, ensure_ascii=False)
            else:
                return str(input_data)

        # Handle Number conversions
        elif to_type == "Number":
            if isinstance(input_data, (int, float)):
                return input_data
            elif isinstance(input_data, str):
                return self._string_to_number(input_data)
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Number")

        # Handle Boolean conversions
        elif to_type == "Boolean":
            if isinstance(input_data, bool):
                return input_data
            elif isinstance(input_data, str):
                return self._string_to_boolean(input_data)
            elif isinstance(input_data, (int, float)):
                return bool(input_data)
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Boolean")

        # Handle Object conversions
        elif to_type == "Object":
            if isinstance(input_data, dict):
                return input_data
            elif isinstance(input_data, str):
                return self._safe_json_parse(input_data)
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Object")

        # Handle Array conversions
        elif to_type == "Array":
            if isinstance(input_data, list):
                return input_data
            elif isinstance(input_data, str):
                parsed = self._safe_json_parse(input_data)
                if isinstance(parsed, list):
                    return parsed
                else:
                    raise ValueError("Parsed JSON is not an array")
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Array")

        # Handle JSON String conversions
        elif to_type == "JSON String":
            if isinstance(input_data, str):
                # Validate it's valid JSON first
                self._safe_json_parse(input_data)
                return input_data
            elif isinstance(input_data, (dict, list)):
                return json.dumps(input_data, indent=2 if pretty_format else None, ensure_ascii=False)
            else:
                return json.dumps(input_data, indent=2 if pretty_format else None, ensure_ascii=False)

        # Handle CSV String conversions
        elif to_type == "CSV String":
            if isinstance(input_data, list):
                return self._array_to_csv_string(input_data, delimiter)
            elif isinstance(input_data, str):
                # Already a string, assume it's CSV
                return input_data
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to CSV String")

        # Handle Joined String conversions
        elif to_type == "Joined String":
            if isinstance(input_data, list):
                return delimiter.join(str(item) for item in input_data)
            elif isinstance(input_data, str):
                return input_data
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Joined String")

        # Handle Split Array conversions
        elif to_type == "Split Array":
            if isinstance(input_data, str):
                return input_data.split(delimiter)
            elif isinstance(input_data, list):
                return input_data
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Split Array")

        # Handle Flattened Object conversions
        elif to_type == "Flattened Object":
            if isinstance(input_data, dict):
                return self._flatten_object(input_data)
            elif isinstance(input_data, str):
                parsed = self._safe_json_parse(input_data)
                if isinstance(parsed, dict):
                    return self._flatten_object(parsed)
                else:
                    raise ValueError("Parsed JSON is not an object")
            else:
                raise ValueError(f"Cannot convert {type(input_data).__name__} to Flattened Object")

        else:
            raise ValueError(f"Unsupported conversion type: {to_type}")

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the data conversion operation.

        Args:
            payload: The request payload containing:
                - input_data: The data to convert
                - from_type: The source data type (optional, auto-detect if not provided)
                - to_type: The target data type
                - delimiter: Delimiter for CSV/Join/Split operations (optional)
                - pretty_format: Whether to use pretty formatting (optional)

        Returns:
            A dictionary containing the result of the operation
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing universal converter request for request_id: {request_id}")
        logger.debug(f"Payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            parameters = payload["tool_parameters"]
            parameters["request_id"] = request_id
        else:
            parameters = payload

        logger.info(f"PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # Get inputs from parameters
            input_data = parameters.get("input_data")
            from_type = parameters.get("from_type", "Auto-detect")
            to_type = parameters.get("to_type")
            delimiter = parameters.get("delimiter", ",")
            pretty_format = parameters.get("pretty_format", True)

            logger.debug(f"Input data type: {type(input_data).__name__} for request_id {request_id}")
            logger.debug(f"Input data content: {repr(input_data)[:200]}... for request_id {request_id}")
            logger.debug(f"From type: {from_type}, To type: {to_type} for request_id {request_id}")

            # Check if input_data is None
            if input_data is None:
                error_msg = f"Input data is None for request_id {request_id}"
                logger.error(error_msg)
                return {"converted_data": None, "error": error_msg}

            # Check if to_type is None or empty
            if not to_type:
                error_msg = f"Target type (to_type) is missing or empty for request_id {request_id}"
                logger.error(error_msg)
                return {"converted_data": None, "error": error_msg}

            # Auto-detect source type if needed
            if from_type == "Auto-detect":
                from_type = self._detect_type(input_data)
                logger.debug(f"Auto-detected source type: {from_type} for request_id {request_id}")

            # Perform the conversion
            try:
                converted_data = self._convert_data(input_data, from_type, to_type, delimiter, pretty_format)
                
                logger.info(f"Data converted successfully for request_id {request_id}. Result type: {type(converted_data).__name__}")
                logger.debug(f"Conversion result for request_id {request_id}: {converted_data}")
                
                return {
                    "converted_data": converted_data,
                    "original_type": from_type,
                    "target_type": to_type,
                    "error": None
                }

            except ValueError as e:
                error_msg = f"Conversion error: {str(e)} for request_id {request_id}"
                logger.error(error_msg)
                return {"converted_data": None, "error": error_msg}

        except Exception as e:
            # Catch all exceptions and return error status
            error_msg = f"Unexpected error during data conversion for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {"converted_data": None, "error": error_msg}


# Verify registration at module load time
try:
    from app.core_.component_system import COMPONENT_REGISTRY
    if "UniversalConverterComponent" in COMPONENT_REGISTRY:
        logger.info("UniversalConverterComponent successfully registered in COMPONENT_REGISTRY")
    else:
        logger.error("UniversalConverterComponent NOT found in COMPONENT_REGISTRY after registration")
except Exception as e:
    logger.error(f"Failed to verify UniversalConverterComponent registration: {e}")