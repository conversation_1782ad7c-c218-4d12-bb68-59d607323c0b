"""
Conditional Component - Evaluates conditions and returns routing decisions.

This component executes as a separate workflow step and returns the target transition
for the orchestration engine to follow. It implements the new component-based
conditional routing architecture with dual-mode support.

Key Features:
- **Dual-Mode Evaluation**: Toggle between direct mode and variable mode
- **Direct Mode**: Conditions evaluate against input_data directly
- **Variable Mode**: Conditions evaluate against a global variable while routing input_data
- Implements 9 comparison operators for flexible condition logic
- Returns structured routing decisions for orchestration engine
- Provides comprehensive error handling and logging
- Supports both single and multiple condition matching strategies

Mode Details:
- **Direct Mode** (use_variable_for_conditions = false):
  - Conditions evaluate against input_data directly
  - Same data used for evaluation and routing
  - Simple, straightforward conditional logic

- **Variable Mode** (use_variable_for_conditions = true):
  - Conditions evaluate against a global variable specified in condition_variable_name
  - input_data flows through unchanged for routing
  - Allows checking external variables while routing main data

"""

from typing import Dict, Any, List, Optional
import logging
from pydantic import BaseModel, Field, validator
from app.core_.base_component import BaseComponent
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


class ConditionSchema(BaseModel):
    """Schema for individual condition validation - simplified for single input approach."""

    operator: str = Field(..., description="Comparison operator")
    expected_value: Any = Field(..., description="Expected value for comparison")
    next_transition: str = Field(..., description="Target transition if condition matches")

    @validator('operator')
    def validate_operator(cls, v):
        """Validate that operator is one of the supported operators."""
        valid_operators = [
            'equals', 'not_equals', 'contains', 'starts_with', 'ends_with',
            'greater_than', 'less_than', 'exists', 'is_empty'
        ]
        if v not in valid_operators:
            raise ValueError(f'operator must be one of: {", ".join(valid_operators)}')
        return v


class ConditionalRequestSchema(BaseModel):
    """Schema for conditional component request validation - supports multiple source types."""

    request_id: str = Field(..., description="Unique request identifier")
    conditions: List[ConditionSchema] = Field(..., description="List of conditions to evaluate")
    input_data: Any = Field(..., description="Input data that will be routed when conditions match")
    default_transition: str = Field(..., description="Default transition if no conditions match")
    source: str = Field(default="node_output", description="Data source for condition evaluation: 'node_output' or 'global_context'")
    allow_multiple_matches: bool = Field(default=False, description="Allow multiple conditions to match simultaneously")
    evaluation_strategy: str = Field(default="first_match", description="Strategy: 'first_match' or 'all_matches'")
    use_variable_for_conditions: bool = Field(default=False, description="Whether to use a global variable for condition evaluation")
    condition_variable_name: Optional[str] = Field(default=None, description="Name of global variable to use for condition evaluation (required if use_variable_for_conditions is True)")
    global_context: Dict[str, Any] = Field(default_factory=dict, description="Global context variables for variable mode")
    # Additional fields for new schema support
    default_ends_at: Optional[List[str]] = Field(default=None, description="Default end transitions")
    tool_parameters: Optional[Dict[str, Any]] = Field(default=None, description="Tool parameters wrapper")

    @validator('conditions')
    def validate_conditions_not_empty(cls, v):
        """Validate that conditions list is not empty."""
        if not v:
            raise ValueError('conditions list cannot be empty')
        return v

    @validator('evaluation_strategy')
    def validate_evaluation_strategy(cls, v):
        """Validate that evaluation strategy is supported."""
        if v not in ['first_match', 'all_matches']:
            raise ValueError('evaluation_strategy must be either "first_match" or "all_matches"')
        return v

    @validator('source')
    def validate_source(cls, v):
        """Validate that source is one of the supported types."""
        valid_sources = ['node_output', 'global_context']
        if v not in valid_sources:
            raise ValueError(f'source must be one of: {", ".join(valid_sources)}')
        return v

    @validator('condition_variable_name', always=True)
    def validate_condition_variable_name(cls, v, values):
        """Validate that condition_variable_name is provided when use_variable_for_conditions is True."""
        if values.get('use_variable_for_conditions') and not v:
            raise ValueError('condition_variable_name is required when use_variable_for_conditions is True')
        return v


@register_component("conditional")
class ConditionalComponent(BaseComponent):
    """
    Conditional routing component that evaluates conditions and returns routing decisions.
    
    This component executes as a separate workflow step and returns the target transition
    for the orchestration engine to follow.
    
    Architecture:
    - Inherits from BaseComponent following standard component pattern
    - Registered with @register_component decorator for discovery
    - Implements async process() method for condition evaluation
    - Returns routing decisions in standardized format
    """
    
    def __init__(self):
        """
        Initialize the ConditionalComponent.

        Sets up the component with proper inheritance and component type.
        """
        super().__init__()
        self.component_type = "conditional"
        self.request_schema = ConditionalRequestSchema
        logger.info("ConditionalComponent initialized successfully")

    async def validate_input(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate input payload against the ConditionalRequestSchema.

        Args:
            payload: Input payload to validate

        Returns:
            Dict containing validation result:
            - is_valid: Boolean indicating if validation passed
            - error_message: String describing validation errors (if any)
            - validated_data: Parsed and validated data (if validation passed)
        """
        try:
            # Attempt to validate the payload using Pydantic schema
            validated_data = self.request_schema(**payload)

            logger.debug(f"Input validation passed for request_id: {payload.get('request_id', 'unknown')}")

            return {
                "is_valid": True,
                "error_message": None,
                "validated_data": validated_data.dict()
            }

        except Exception as e:
            error_message = str(e)
            logger.warning(f"Input validation failed: {error_message}")

            return {
                "is_valid": False,
                "error_message": error_message,
                "validated_data": None
            }
    
    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process conditional routing logic.

        This is the main entry point for conditional evaluation. Integrates validation,
        condition evaluation, and routing decision logic.

        Args:
            payload: Contains conditions, global context, and previous node output

        Returns:
            Dict containing routing decision and metadata
        """
        import time
        start_time = time.time()

        try:
            request_id = payload.get("request_id", "unknown")
            logger.info(f"Processing conditional routing for request_id: {request_id}")

            # Step 1: Validate input payload
            validation_result = await self.validate_input(payload)
            if not validation_result["is_valid"]:
                execution_time = (time.time() - start_time) * 1000
                logger.warning(f"Input validation failed: {validation_result['error_message']}")

                return {
                    "status": "error",
                    "error": validation_result["error_message"],
                    "routing_decision": {
                        "target_transition": payload.get("default_transition", "default"),
                        "matched_condition": None,
                        "condition_result": False,
                        "execution_time_ms": execution_time
                    },
                    "metadata": {
                        "total_conditions": 0,
                        "evaluation_order": None
                    },
                    "input_data": payload.get("input_data")  # Include original input data for data flow
                }

            # Step 2: Extract validated data
            validated_data = validation_result["validated_data"]
            conditions = validated_data["conditions"]
            input_data = validated_data["input_data"]
            default_transition = validated_data["default_transition"]
            source = validated_data.get("source", "node_output")
            allow_multiple_matches = validated_data.get("allow_multiple_matches", False)
            evaluation_strategy = validated_data.get("evaluation_strategy", "first_match")
            use_variable_for_conditions = validated_data.get("use_variable_for_conditions", False)
            condition_variable_name = validated_data.get("condition_variable_name")
            global_context = validated_data.get("global_context", {})

            # Step 3: Determine evaluation data based on source
            if source == "node_output":
                # Node output mode: conditions evaluate against input_data
                # For the new schema, if input_data has nested input_data, extract it
                if isinstance(input_data, dict) and "input_data" in input_data:
                    evaluation_data = input_data["input_data"]
                    logger.info(f"Node output mode: evaluating conditions against nested input_data.input_data: {evaluation_data}")
                else:
                    evaluation_data = input_data
                    logger.info(f"Node output mode: evaluating conditions against input_data directly: {evaluation_data}")

            elif source == "global_context":
                # Global context mode: conditions evaluate against a global variable
                if use_variable_for_conditions and condition_variable_name:
                    # Get the variable value from global context
                    evaluation_data = global_context.get(condition_variable_name)
                    if evaluation_data is None:
                        logger.warning(f"Variable '{condition_variable_name}' not found in global context, using empty string")
                        evaluation_data = ""
                    logger.info(f"Global context mode: evaluating conditions against variable '{condition_variable_name}': {evaluation_data}")
                else:
                    execution_time = (time.time() - start_time) * 1000
                    error_msg = "Global context mode requires use_variable_for_conditions=True and condition_variable_name"
                    logger.warning(error_msg)

                    return {
                        "status": "error",
                        "error": error_msg,
                        "routing_decision": {
                            "target_transition": default_transition,
                            "matched_condition": None,
                            "condition_result": False,
                            "execution_time_ms": execution_time
                        },
                        "metadata": {
                            "total_conditions": len(conditions),
                            "evaluation_order": None,
                            "evaluation_mode": source,
                            "condition_variable_name": condition_variable_name
                        },
                        "input_data": input_data
                    }
            else:
                execution_time = (time.time() - start_time) * 1000
                error_msg = f"Unknown source type: {source}"
                logger.warning(error_msg)

                return {
                    "status": "error",
                    "error": error_msg,
                    "routing_decision": {
                        "target_transition": default_transition,
                        "matched_condition": None,
                        "condition_result": False,
                        "execution_time_ms": execution_time
                    },
                    "metadata": {
                        "total_conditions": len(conditions),
                        "evaluation_order": None,
                        "evaluation_mode": source
                    },
                    "input_data": input_data
                }

            # Step 4: Evaluate conditions based on strategy
            if evaluation_strategy == "first_match":
                # Original behavior: first match wins
                return await self._evaluate_first_match(conditions, evaluation_data, input_data, default_transition, start_time, source, condition_variable_name)
            elif evaluation_strategy == "all_matches":
                # New behavior: evaluate all conditions and return multiple matches
                return await self._evaluate_all_matches(conditions, evaluation_data, input_data, default_transition, start_time, source, condition_variable_name)

            # Fallback - should not reach here if strategies are implemented correctly
            logger.error(f"Unknown evaluation strategy: {evaluation_strategy}")
            execution_time = (time.time() - start_time) * 1000

            return {
                "status": "error",
                "error": f"Unknown evaluation strategy: {evaluation_strategy}",
                "routing_decision": {
                    "target_transition": default_transition,
                    "matched_condition": None,
                    "condition_result": False,
                    "execution_time_ms": execution_time
                },
                "metadata": {
                    "total_conditions": len(conditions),
                    "evaluation_order": None
                },
                "input_data": input_data  # Include original input data for data flow
            }

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"Error in conditional routing: {e}", exc_info=True)

            return {
                "status": "error",
                "error": str(e),
                "routing_decision": {
                    "target_transitions": [payload.get("default_transition", "default")],  # Array format for consistency
                    "matched_conditions": [],
                    "condition_result": False,
                    "execution_time_ms": execution_time
                },
                "metadata": {
                    "total_conditions": 0,
                    "total_matches": 0,
                    "evaluation_strategy": payload.get("evaluation_strategy", "first_match")
                },
                "input_data": payload.get("node_output")  # Include original input data for data flow
            }

    async def _evaluate_condition(
        self,
        condition: Dict[str, Any],
        input_data: Any
    ) -> bool:
        """
        Evaluate a single condition against the input data.

        Args:
            condition: Condition configuration dict
            input_data: The single input data that all conditions evaluate against

        Returns:
            Boolean result of condition evaluation
        """
        try:
            operator = condition.get("operator", "equals")
            expected_value = condition.get("expected_value")

            # All conditions evaluate against the same input_data
            actual_value = input_data

            # Apply operator
            return self._apply_operator(operator, actual_value, expected_value)

        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False

    def _apply_operator(self, operator: str, actual_value: Any, expected_value: Any) -> bool:
        """
        Apply comparison operator to values.

        Args:
            operator: Comparison operator string
            actual_value: Actual value from data source
            expected_value: Expected value for comparison

        Returns:
            Boolean result of operator application
        """
        try:
            if operator == "equals":
                return actual_value == expected_value
            elif operator == "not_equals":
                return actual_value != expected_value
            elif operator == "contains":
                return str(expected_value) in str(actual_value) if actual_value is not None else False
            elif operator == "starts_with":
                return str(actual_value).startswith(str(expected_value)) if actual_value is not None else False
            elif operator == "ends_with":
                return str(actual_value).endswith(str(expected_value)) if actual_value is not None else False
            elif operator == "greater_than":
                return float(actual_value) > float(expected_value)
            elif operator == "less_than":
                return float(actual_value) < float(expected_value)
            elif operator == "exists":
                return actual_value is not None
            elif operator == "is_empty":
                return actual_value is None or actual_value == "" or actual_value == [] or actual_value == {}
            else:
                logger.warning(f"Unknown operator: {operator}")
                return False
        except Exception as e:
            logger.error(f"Error applying operator {operator}: {e}")
            return False

    async def _evaluate_first_match(
        self,
        conditions: List[Dict[str, Any]],
        evaluation_data: Any,
        input_data: Any,
        default_transition: str,
        start_time: float,
        source: str = "node_output",
        condition_variable_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate conditions with first-match strategy (original behavior).

        Args:
            conditions: List of condition configurations
            evaluation_data: The data to evaluate conditions against
            input_data: The input data that will be routed
            default_transition: Default transition if no conditions match
            start_time: Start time for execution timing
            source: Data source type ('node_output' or 'global_context')
            condition_variable_name: Name of the condition variable if using global_context

        Returns:
            Routing decision with single target transition
        """
        import time

        # Evaluate conditions sequentially (first match wins)
        for i, condition in enumerate(conditions):
            condition_matched = await self._evaluate_condition(condition, evaluation_data)

            if condition_matched:
                target_transition = condition.get("next_transition")
                execution_time = (time.time() - start_time) * 1000

                logger.info(f"Condition {i+1} matched, routing to: {target_transition}")

                return {
                    "status": "success",
                    "routing_decision": {
                        "target_transition": target_transition,
                        "matched_condition": i + 1,
                        "condition_result": True,
                        "execution_time_ms": execution_time
                    },
                    "metadata": {
                        "total_conditions": len(conditions),
                        "evaluation_order": i + 1,
                        "evaluation_mode": source,
                        "condition_variable_name": condition_variable_name if source == "global_context" else None
                    },
                    "input_data": input_data  # Include original input data for data flow
                }

        # No conditions matched, use default transition
        execution_time = (time.time() - start_time) * 1000
        logger.info(f"No conditions matched, using default: {default_transition}")

        return {
            "status": "success",
            "routing_decision": {
                "target_transition": default_transition,
                "matched_condition": None,
                "condition_result": False,
                "execution_time_ms": execution_time
            },
            "metadata": {
                "total_conditions": len(conditions),
                "evaluation_order": None,
                "evaluation_mode": source,
                "condition_variable_name": condition_variable_name if source == "global_context" else None
            },
            "input_data": input_data  # Include original input data for data flow
        }

    async def _evaluate_all_matches(
        self,
        conditions: List[Dict[str, Any]],
        evaluation_data: Any,
        input_data: Any,
        default_transition: str,
        start_time: float,
        source: str = "node_output",
        condition_variable_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate conditions with all-matches strategy (new behavior for multiple transitions).

        Args:
            conditions: List of condition configurations
            evaluation_data: The data to evaluate conditions against
            input_data: The input data that will be routed
            default_transition: Default transition if no conditions match
            start_time: Start time for execution timing
            source: Data source type ('node_output' or 'global_context')
            condition_variable_name: Name of the condition variable if using global_context

        Returns:
            Routing decision with multiple target transitions
        """
        import time

        matched_transitions = []
        matched_conditions = []

        # Evaluate all conditions and collect matches
        for i, condition in enumerate(conditions):
            condition_matched = await self._evaluate_condition(condition, evaluation_data)

            if condition_matched:
                target_transition = condition.get("next_transition")
                matched_transitions.append(target_transition)
                matched_conditions.append(i + 1)
                logger.info(f"Condition {i+1} matched, will route to: {target_transition}")

        execution_time = (time.time() - start_time) * 1000

        # If conditions matched, return multiple transitions
        if matched_transitions:
            logger.info(f"Multiple conditions matched ({len(matched_transitions)}), routing to: {matched_transitions}")

            return {
                "status": "success",
                "routing_decision": {
                    "target_transitions": matched_transitions,  # Array of transitions
                    "matched_conditions": matched_conditions,
                    "condition_result": True,
                    "execution_time_ms": execution_time
                },
                "metadata": {
                    "total_conditions": len(conditions),
                    "total_matches": len(matched_transitions),
                    "evaluation_strategy": "all_matches",
                    "evaluation_mode": source,
                    "condition_variable_name": condition_variable_name if source == "global_context" else None
                },
                "input_data": input_data  # Include original input data for data flow
            }

        # No conditions matched, use default transition
        logger.info(f"No conditions matched, using default: {default_transition}")

        return {
            "status": "success",
            "routing_decision": {
                "target_transitions": [default_transition],  # Array format for consistency
                "matched_conditions": [],
                "condition_result": False,
                "execution_time_ms": execution_time
            },
            "metadata": {
                "total_conditions": len(conditions),
                "total_matches": 0,
                "evaluation_strategy": "all_matches",
                "evaluation_mode": source,
                "condition_variable_name": condition_variable_name if source == "global_context" else None
            },
            "input_data": input_data  # Include original input data for data flow
        }
